import os
import uuid
import json
import base64
import asyncio
import websockets
import gzip
import weakref
import threading
from datetime import datetime
from typing import Optional, Dict, ClassVar
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()


class ClientEventLoopManager:
    """客户端专属的事件循环管理器"""
    def __init__(self, client_id: str, parent_connection_manager=None):
        self.client_id = client_id
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.thread: Optional[threading.Thread] = None
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._lock = threading.Lock()
        self._started = False
        self._stopping = False
        # 【CPU修复】保存父连接管理器的引用，用于检查清理状态
        self._parent_connection_manager = parent_connection_manager
        # 添加请求队列，避免并发访问WebSocket
        self.request_queue: Optional[asyncio.Queue] = None
        self.worker_task: Optional[asyncio.Task] = None
        # 序列号计数器（线程安全）
        self._sequence_counter = 0
        self._sequence_lock = threading.Lock()
        # 重连相关参数
        self.max_retry_attempts = 3
        self.retry_delay_base = 1  # 基础重连延迟（秒）
        self.current_retry_count = 0
        self.connection_config = None  # 保存连接配置用于重连
        
    def start(self):
        """启动事件循环线程"""
        with self._lock:
            if self._started:
                return
            self._started = True
            
            self.thread = threading.Thread(
                target=self._run_event_loop,
                daemon=True,
                name=f"TTS-EventLoop-{self.client_id[:8]}"
            )
            self.thread.start()
            
            # 等待事件循环就绪
            while self.loop is None:
                threading.Event().wait(0.01)
                
    def _run_event_loop(self):
        """在独立线程中运行事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        # 创建请求队列
        self.request_queue = asyncio.Queue()
        logger.bind(tag=TAG).info(f"为客户端 {self.client_id} 创建独立事件循环")
        
        try:
            self.loop.run_forever()
        finally:
            self.loop.close()
            logger.bind(tag=TAG).info(f"客户端 {self.client_id} 的事件循环已关闭")
            
    async def _request_worker(self):
        """处理请求队列的工作协程"""
        logger.bind(tag=TAG).info(f"[{self.client_id}] 🚀 请求处理工作协程启动")
        try:
            while True:
                try:
                    # 【重要修复】添加超时机制避免无限等待，缩短超时时间以便更快检测断开连接
                    logger.bind(tag=TAG).debug(f"[{self.client_id}] 📥 等待队列中的请求...")
                    request_data, future = await asyncio.wait_for(
                        self.request_queue.get(),
                        timeout=10.0  # 10秒超时，更快检测断开连接
                    )
                    
                    if request_data is None:  # 停止信号
                        logger.bind(tag=TAG).info(f"[{self.client_id}] 🛑 收到停止信号，退出工作协程")
                        break
                        
                    seq_id = request_data.get("sequence_id", "unknown")
                    logger.bind(tag=TAG).info(f"[{self.client_id}][seq:{seq_id}] 📋 开始处理TTS请求（队列模式确保串行）")
                    
                    # 记录请求间隔时间（用于检查是否需要延迟）
                    current_time = asyncio.get_event_loop().time()
                    if hasattr(self, '_last_request_time'):
                        interval = (current_time - self._last_request_time) * 1000
                        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{seq_id}] ⏱️ 距离上次请求间隔: {interval:.1f}ms")
                    self._last_request_time = current_time
                        
                    try:
                        # 执行TTS请求
                        result = await self._process_tts_request(request_data)
                        if not future.done():  # 【修复】确保future未被设置
                            future.set_result(result)
                        logger.bind(tag=TAG).info(f"[{self.client_id}][seq:{seq_id}] ✅ TTS请求处理完成")
                    except Exception as e:
                        logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{seq_id}] ❌ TTS请求处理失败: {e}")
                        if not future.done():  # 【修复】确保future未被设置
                            future.set_exception(e)
                        
                except asyncio.TimeoutError:
                    # 【CPU修复增强】超时时检查是否应该退出
                    if self._stopping:
                        logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ 队列等待超时且正在停止，退出工作协程")
                        break

                    # 【CPU修复】检查客户端是否已被标记为清理中
                    if hasattr(self, '_parent_connection_manager'):
                        if (hasattr(self._parent_connection_manager, '_cleanup_in_progress') and
                            self.client_id in self._parent_connection_manager._cleanup_in_progress):
                            logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ 客户端正在清理中，退出工作协程")
                            break

                    # 【CPU修复】检查WebSocket连接状态
                    if self.websocket:
                        try:
                            if hasattr(self.websocket, 'state'):
                                from websockets.protocol import State
                                if self.websocket.state in (State.CLOSED, State.CLOSING):
                                    logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ WebSocket连接已关闭，退出工作协程")
                                    break
                        except Exception as state_check_error:
                            logger.bind(tag=TAG).warning(f"[{self.client_id}] ⏰ 检查WebSocket状态失败: {state_check_error}")
                            break

                    # 【新增】检查客户端管理器是否还存在于全局管理器中
                    if (hasattr(self, '_parent_connection_manager') and
                        hasattr(self._parent_connection_manager, 'client_managers') and
                        self.client_id not in self._parent_connection_manager.client_managers):
                        logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ 客户端管理器已被移除，退出工作协程")
                        break

                    # 【新增】检查事件循环是否正在关闭
                    try:
                        if self.loop and self.loop.is_closed():
                            logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ 事件循环已关闭，退出工作协程")
                            break
                    except Exception:
                        # 如果检查事件循环状态出错，也退出
                        logger.bind(tag=TAG).info(f"[{self.client_id}] ⏰ 无法检查事件循环状态，退出工作协程")
                        break

                    logger.bind(tag=TAG).debug(f"[{self.client_id}] ⏰ 队列等待超时，继续等待...")
                    continue
                    
                except Exception as e:
                    logger.bind(tag=TAG).error(f"[{self.client_id}] 📦 请求处理工作协程出错: {e}")
                    # 【重要修复】严重错误时退出循环避免无限重试
                    if "cannot schedule new futures" in str(e) or "Event loop is closed" in str(e):
                        logger.bind(tag=TAG).error(f"[{self.client_id}] 💥 事件循环已关闭，退出工作协程")
                        break
                    # 其他错误继续尝试
                    await asyncio.sleep(1)  # 短暂延迟避免忙等待
                    
        except Exception as fatal_error:
            logger.bind(tag=TAG).error(f"[{self.client_id}] 💀 工作协程致命错误: {fatal_error}")
        finally:
            logger.bind(tag=TAG).info(f"[{self.client_id}] 🏁 请求处理工作协程已退出")
                
    async def _process_tts_request(self, request_data):
        """处理单个TTS请求"""
        text = request_data["text"]
        output_file = request_data["output_file"]
        sequence_id = request_data["sequence_id"]
        voice_config = request_data.get("voice_config", {})  # 【重要修复】获取声音配置
        
        # 【声音配置日志3】在请求处理工作协程中记录收到的声音配置
        logger.bind(tag=TAG).info(f"[{self.client_id}][seq:{sequence_id}] 🎭 请求处理工作协程收到声音配置: {voice_config}")
        
        # 确保WebSocket连接存在
        if not self.websocket:
            raise Exception("WebSocket连接不存在")
            
        # 【CPU优化】禁用发送前积压检测，减少CPU占用
        # await self._check_pending_data("发送前", sequence_id)
            
        # 构建二进制消息
        binary_message = request_data["binary_message"]
        
        # 发送请求并接收响应
        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 准备发送二进制消息，大小: {len(binary_message)}字节")
        
        # 记录WebSocket连接状态 - 安全检查属性
        try:
            closed_status = getattr(self.websocket, 'closed', 'unknown')
            state_status = getattr(self.websocket, 'state', 'unknown')
            logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] WebSocket状态: 关闭={closed_status}, 状态={state_status}, 类型={type(self.websocket)}")
        except Exception as e:
            logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 无法获取WebSocket状态: {e}")
        
        # 发送消息，如果失败尝试重连
        send_success = False
        for attempt in range(self.max_retry_attempts + 1):
            try:
                send_start_time = asyncio.get_event_loop().time()
                await self.websocket.send(binary_message)
                send_duration = (asyncio.get_event_loop().time() - send_start_time) * 1000
                
                logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 已发送TTS请求: {text[:50]}... (耗时: {send_duration:.1f}ms)")
                send_success = True
                break
                
            except Exception as e:
                logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 发送消息失败: {e}")
                
                # 如果是连接相关的错误，尝试重连
                if attempt < self.max_retry_attempts and self._is_connection_error(e):
                    logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 检测到连接错误，尝试重连...")
                    reconnect_success = await self._attempt_reconnect()
                    if not reconnect_success:
                        break
                else:
                    raise e
                    
        if not send_success:
            raise Exception(f"发送消息失败，已尝试 {self.max_retry_attempts + 1} 次")
        
        # 接收响应
        audio_chunks = []
        response_count = 0
        
        try:
            while True:
                recv_success = False
                recv_attempts = 0
                max_recv_attempts = 2  # 接收时只重试2次，避免无限循环
                
                while not recv_success and recv_attempts < max_recv_attempts:
                    try:
                        # 添加接收超时
                        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 等待服务端响应，超时时间: {request_data['timeout']}秒")
                        response_data = await asyncio.wait_for(
                            self.websocket.recv(), 
                            timeout=request_data["timeout"]
                        )
                        recv_success = True
                        
                    except Exception as recv_error:
                        recv_attempts += 1
                        logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 接收消息失败 (尝试 {recv_attempts}/{max_recv_attempts}): {recv_error}")
                        
                        # 如果是连接错误且还有重试机会，尝试重连
                        if recv_attempts < max_recv_attempts and self._is_connection_error(recv_error):
                            logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 接收时检测到连接错误，尝试重连...")
                            reconnect_success = await self._attempt_reconnect()
                            if not reconnect_success:
                                break
                            # 重连成功后，需要重新发送请求
                            try:
                                await self.websocket.send(binary_message)
                                logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 重连后重新发送请求")
                            except Exception as resend_error:
                                logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 重连后重新发送失败: {resend_error}")
                                break
                        else:
                            raise recv_error
                            
                if not recv_success:
                    raise Exception(f"接收消息失败，已尝试 {max_recv_attempts} 次")
                    
                response_count += 1
                data_size = len(response_data) if hasattr(response_data, '__len__') else 'N/A'
                logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 收到第{response_count}个响应，类型: {type(response_data)}, 大小: {data_size}")
                
                # 【积压检测2】如果接收到异常大的数据，记录详细信息
                if hasattr(response_data, '__len__') and len(response_data) > 1000000:  # 超过1MB
                    logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 🚨 收到异常大的响应数据: {len(response_data)}字节")
                    # 【CPU优化】禁用大数据后积压检测，减少CPU占用
                    # await self._check_pending_data("收到大数据后", sequence_id)
                    
                if isinstance(response_data, bytes):
                    # 解析二进制响应
                    response = self.parse_binary_response(response_data)
                    
                    if response.get("type") == "error":
                        raise Exception(f"服务端错误: {response.get('code')} - {response.get('message')}")
                    
                    elif response.get("type") == "ack":
                        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 收到服务端确认")
                        continue
                    
                    elif response.get("type") == "audio":
                        # 音频数据
                        sequence = response.get("sequence", 0)
                        audio_data = base64.b64decode(response["data"])
                        audio_chunks.append(audio_data)
                        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 收到音频块: 序列号={sequence}, 大小={len(audio_data)}字节")
                        
                        # 检查是否完成（序列号小于0表示结束）
                        if sequence < 0:
                            logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 语音合成完成")
                            break
                    
                    elif "code" in response:
                        # 兼容旧格式的响应
                        if response["code"] != 3000:
                            raise Exception(f"服务端返回错误: {response['code']}")
                        
                        if "data" in response and response["data"]:
                            # 解码音频数据
                            audio_data = base64.b64decode(response["data"])
                            audio_chunks.append(audio_data)
                        
                        # 检查是否完成
                        if "sequence" in response and response["sequence"] < 0:
                            logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 语音合成完成")
                            break
                    
                    else:
                        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 未处理的响应类型: {response}")
                        
                else:
                    # JSON响应
                    response = json.loads(response_data)
                    logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] 收到JSON响应: {response}")
                    
        except asyncio.TimeoutError:
            logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 接收响应超时，已等待{request_data['timeout']}秒，收到{response_count}个响应")
            raise Exception("接收响应超时")
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 接收数据异常: {e}")
            # 【CPU优化】禁用异常时积压检测，减少CPU占用
            # await self._check_pending_data("异常发生时", sequence_id)
            raise

        # 合并所有音频块并保存到文件
        if audio_chunks:
            total_audio = b''.join(audio_chunks)
            with open(output_file, "wb") as f:
                f.write(total_audio)
            logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] DoubaoTTS生成成功: {text[:20]}..., 文件大小: {len(total_audio)}字节")
            return True
        else:
            raise Exception("未收到音频数据")
            
    def parse_binary_response(self, data: bytes) -> dict:
        """解析二进制响应消息（移到这里供_process_tts_request使用）"""
        if len(data) < 4:
            raise ValueError("Invalid response: too short")
        
        # 根据官方Demo的解析方式
        protocol_version = data[0] >> 4
        header_size = data[0] & 0x0f
        message_type = data[1] >> 4
        message_type_specific_flags = data[1] & 0x0f
        serialization_method = data[2] >> 4
        message_compression = data[2] & 0x0f
        reserved = data[3]
        
        logger.bind(tag=TAG).debug(f"WebSocket响应解析: 消息类型={message_type}, 标志={message_type_specific_flags}, 压缩={message_compression}")
        
        header_extensions = data[4:header_size*4]
        payload = data[header_size*4:]
        
        if message_type == 0xb:  # audio-only server response
            if message_type_specific_flags == 0:  # no sequence number as ACK
                logger.bind(tag=TAG).debug("收到ACK响应")
                return {"type": "ack"}
            else:
                sequence_number = int.from_bytes(payload[:4], "big", signed=True)
                payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                audio_payload = payload[8:]
                logger.bind(tag=TAG).debug(f"收到音频数据: 序列号={sequence_number}, 大小={payload_size}")
                return {
                    "type": "audio",
                    "sequence": sequence_number,
                    "data": base64.b64encode(audio_payload).decode('utf-8')
                }
        elif message_type == 0xf:  # error message
            code = int.from_bytes(payload[:4], "big", signed=False)
            msg_size = int.from_bytes(payload[4:8], "big", signed=False)
            error_msg = payload[8:]
            if message_compression == 1:
                error_msg = gzip.decompress(error_msg)
            error_msg = str(error_msg, "utf-8")
            logger.bind(tag=TAG).error(f"收到错误消息: 代码={code}, 消息={error_msg}")
            return {
                "type": "error",
                "code": code,
                "message": error_msg
            }
        elif message_type == 0xc:  # frontend server response
            msg_size = int.from_bytes(payload[:4], "big", signed=False)
            response_payload = payload[4:]
            if message_compression == 1:
                response_payload = gzip.decompress(response_payload)
            try:
                response_json = json.loads(response_payload.decode('utf-8'))
                logger.bind(tag=TAG).debug(f"收到前端响应: {response_json}")
                return response_json
            except Exception as e:
                logger.bind(tag=TAG).error(f"前端响应解析失败: {e}")
                return {"error": "Failed to parse frontend response"}
        else:
            logger.bind(tag=TAG).warning(f"未知消息类型: {message_type}")
            return {"error": f"Unknown message type: {message_type}"}

    async def _check_pending_data(self, stage: str, sequence_id: int):
        """检查WebSocket中是否有积压的待接收数据"""
        try:
            # 首先检查WebSocket对象是否有效
            if not self.websocket:
                logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] {stage}积压检测跳过: WebSocket连接不存在")
                return
                
            # 检查WebSocket对象类型和属性
            websocket_type = type(self.websocket)
            logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] WebSocket对象类型: {websocket_type}")
            
            # 检查是否有recv方法
            if not hasattr(self.websocket, 'recv'):
                logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] {stage}积压检测跳过: WebSocket对象没有recv方法")
                return
            
            pending_count = 0
            total_size = 0
            
            logger.bind(tag=TAG).info(f"[{self.client_id}][seq:{sequence_id}] 🔍 {stage}积压数据检测开始")
            
            # 【重要修复】优化积压检测逻辑，避免频繁的0.001秒超时导致CPU占用
            start_time = asyncio.get_event_loop().time()
            max_checks = 10  # 限制最大检查次数
            check_count = 0
            
            while check_count < max_checks:
                try:
                    # 【修复】使用更合理的超时时间，避免过于频繁的超时异常
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=0.01)  # 改为10ms
                    pending_count += 1
                    check_count += 1
                    
                    if isinstance(message, bytes):
                        data_size = len(message)
                        total_size += data_size
                        logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 📦 {stage}发现积压二进制数据 #{pending_count}: {data_size}字节")
                        
                        # 如果是二进制数据，尝试解析看看是什么类型
                        try:
                            if len(message) >= 4:
                                parsed = self.parse_binary_response(message)
                                logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 📦 积压数据解析: {parsed.get('type', 'unknown')}")
                        except Exception as parse_error:
                            logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 📦 积压数据解析失败: {parse_error}")
                    else:
                        data_size = len(str(message))
                        total_size += data_size
                        logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] 📄 {stage}发现积压文本数据 #{pending_count}: {message[:200]}...")
                    
                    # 防止无限循环检测
                    if pending_count > 20:  # 降低阈值
                        logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] ⚠️ {stage}积压数据过多({pending_count}条)，停止检测")
                        break
                        
                    # 防止检测时间过长
                    if asyncio.get_event_loop().time() - start_time > 0.05:  # 降低到50ms
                        logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{sequence_id}] ⏰ {stage}积压检测超时，停止检测")
                        break
                        
                except asyncio.TimeoutError:
                    # 【修复】没有更多数据时立即退出，避免继续检查
                    break
                except Exception as e:
                    logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] ❌ {stage}积压检测异常: {e}")
                    break
            
            if pending_count > 0:
                logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] 🚨 {stage}检测到积压数据: {pending_count}条消息, 总大小: {total_size}字节")
            else:
                logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{sequence_id}] ✅ {stage}无积压数据")
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{self.client_id}][seq:{sequence_id}] ❌ {stage}积压检测失败: {e}")

    async def create_websocket(self, access_token: str, cluster: str, config: dict) -> websockets.WebSocketServerProtocol:
        """在专属事件循环中创建WebSocket连接"""
        # 从配置中读取WebSocket地址
        if config and config.get("api_url") and config["api_url"].startswith("wss://"):
            ws_url = config["api_url"]
        else:
            ws_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
            
        logger.bind(tag=TAG).debug(f"[{self.client_id}] 使用WebSocket地址: {ws_url}")
        logger.bind(tag=TAG).debug(f"[{self.client_id}] Access token长度: {len(access_token)} 字符")
        
        # 检查认证头格式 - 注意官方文档要求的格式
        auth_header = f"Bearer; {access_token}"
        headers = {"Authorization": auth_header}
        logger.bind(tag=TAG).debug(f"[{self.client_id}] 使用认证头: Authorization: {auth_header[:50]}...")
        
        self.websocket = await websockets.connect(
            ws_url,
            additional_headers=headers,
            ping_interval=30,
            ping_timeout=10,
            close_timeout=10,
            max_size=2**20,  # 1MB WebSocket帧大小限制
            max_queue=32     # 限制队列大小
        )
        
        logger.bind(tag=TAG).info(f"[{self.client_id}] WebSocket连接建立成功")
        
        # 检查WebSocket对象的属性
        websocket_attrs = [attr for attr in dir(self.websocket) if not attr.startswith('_')]
        logger.bind(tag=TAG).debug(f"[{self.client_id}] WebSocket对象属性: {websocket_attrs[:10]}...")  # 只显示前10个
        
        
        # 【CPU优化】禁用连接建立后积压检测，减少CPU占用
        # await self._check_pending_data("连接建立后", 0)
        
        # 保存连接配置用于重连
        self.connection_config = {
            'access_token': access_token,
            'cluster': cluster,
            'config': config
        }
        
        # 重置重连计数器
        self.current_retry_count = 0
        
        # 启动请求处理工作协程
        self.worker_task = asyncio.create_task(self._request_worker())
        logger.bind(tag=TAG).debug(f"[{self.client_id}] 请求处理工作协程已启动")
        
        return self.websocket
        
    async def submit_request(self, request_data):
        """提交TTS请求到队列"""
        seq_id = request_data.get("sequence_id", "unknown")
        logger.bind(tag=TAG).info(f"[{self.client_id}][seq:{seq_id}] 📤 提交TTS请求到队列")
        
        # 检查队列大小
        queue_size = self.request_queue.qsize()
        if queue_size > 0:
            logger.bind(tag=TAG).warning(f"[{self.client_id}][seq:{seq_id}] ⚠️ 队列中已有 {queue_size} 个待处理请求")
        
        future = asyncio.Future()
        await self.request_queue.put((request_data, future))
        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{seq_id}] 📥 请求已加入队列，等待处理...")
        
        result = await future
        logger.bind(tag=TAG).debug(f"[{self.client_id}][seq:{seq_id}] 🎯 请求处理结果返回")
        return result
        
    def run_coroutine(self, coro):
        """在专属事件循环中运行协程"""
        if not self._started:
            self.start()
            
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        return future.result()
        
    async def close_websocket(self):
        """关闭WebSocket连接"""
        # 停止工作协程
        if self.request_queue:
            try:
                # 【重要修复】添加超时保护，防止队列满时阻塞
                await asyncio.wait_for(self.request_queue.put((None, None)), timeout=2)  # 发送停止信号
                logger.bind(tag=TAG).info(f"[{self.client_id}] 已发送停止信号到请求队列")
            except asyncio.TimeoutError:
                logger.bind(tag=TAG).error(f"[{self.client_id}] 发送停止信号超时，队列可能已满")
            except Exception as e:
                logger.bind(tag=TAG).error(f"[{self.client_id}] 发送停止信号失败: {e}")
                
        if self.worker_task:
            try:
                await asyncio.wait_for(self.worker_task, timeout=5)
                logger.bind(tag=TAG).info(f"[{self.client_id}] 工作协程已正常退出")
            except asyncio.TimeoutError:
                logger.bind(tag=TAG).warning(f"[{self.client_id}] 工作协程5秒内未退出，强制取消")
                self.worker_task.cancel()
                try:
                    await self.worker_task
                except asyncio.CancelledError:
                    logger.bind(tag=TAG).info(f"[{self.client_id}] 工作协程已被取消")
            except Exception as e:
                logger.bind(tag=TAG).error(f"[{self.client_id}] 等待工作协程结束时出错: {e}")
                
        if self.websocket:
            try:
                await self.websocket.close()
                logger.bind(tag=TAG).info(f"[{self.client_id}] WebSocket连接已关闭")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"[{self.client_id}] 关闭WebSocket时出错: {e}")
            finally:
                self.websocket = None

    async def _attempt_reconnect(self) -> bool:
        """尝试重新连接WebSocket"""
        if not self.connection_config:
            logger.bind(tag=TAG).error(f"[{self.client_id}] 无法重连：缺少连接配置")
            return False
            
        if self.current_retry_count >= self.max_retry_attempts:
            logger.bind(tag=TAG).error(f"[{self.client_id}] 重连失败：已达到最大重试次数 {self.max_retry_attempts}")
            return False
            
        self.current_retry_count += 1
        
        # 计算指数退避延迟
        delay = self.retry_delay_base * (2 ** (self.current_retry_count - 1))
        logger.bind(tag=TAG).warning(f"[{self.client_id}] 尝试第 {self.current_retry_count} 次重连，延迟 {delay} 秒...")
        
        await asyncio.sleep(delay)
        
        try:
            # 关闭旧连接
            if self.websocket:
                try:
                    await self.websocket.close()
                except:
                    pass
                self.websocket = None
            
            # 创建新连接
            access_token = self.connection_config['access_token']
            cluster = self.connection_config['cluster']
            config = self.connection_config['config']
            
            ws_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
            if config and config.get("api_url") and config["api_url"].startswith("wss://"):
                ws_url = config["api_url"]
                
            auth_header = f"Bearer; {access_token}"
            headers = {"Authorization": auth_header}
            
            self.websocket = await websockets.connect(
                ws_url,
                additional_headers=headers,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10,
                max_size=2**20,
                max_queue=32
            )
            
            logger.bind(tag=TAG).info(f"[{self.client_id}] 重连成功！第 {self.current_retry_count} 次尝试")
            
            # 重连成功后重置计数器
            self.current_retry_count = 0
            
            # 【重要优化】重连成功后检查工作协程状态，避免不必要的取消和重建
            if self.worker_task and not self.worker_task.done():
                logger.bind(tag=TAG).debug(f"[{self.client_id}] 工作协程仍在运行，无需重新启动")
            else:
                # 只在必要时重新启动工作协程
                if self.worker_task and not self.worker_task.done():
                    self.worker_task.cancel()
                    try:
                        await self.worker_task
                    except asyncio.CancelledError:
                        pass
                self.worker_task = asyncio.create_task(self._request_worker())
                logger.bind(tag=TAG).debug(f"[{self.client_id}] 重连后重新启动请求处理工作协程")
            
            return True
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{self.client_id}] 第 {self.current_retry_count} 次重连失败: {e}")
            return False
            
    def _is_connection_error(self, error) -> bool:
        """判断是否是连接相关的错误 - 使用通用的异常类型检测"""
        # 首先检查异常类型
        if isinstance(error, (
            # WebSocket相关异常
            websockets.exceptions.ConnectionClosed,
            websockets.exceptions.ConnectionClosedError,
            websockets.exceptions.ConnectionClosedOK,
            websockets.exceptions.InvalidState,
            websockets.exceptions.ProtocolError,
            # 网络相关异常
            ConnectionError,
            ConnectionResetError,
            ConnectionAbortedError,
            ConnectionRefusedError,
            BrokenPipeError,
            # 超时异常
            asyncio.TimeoutError,
            TimeoutError,
            # 操作系统网络错误
            OSError
        )):
            return True
            
        # 检查WebSocket连接状态
        try:
            if self.websocket:
                # 检查WebSocket状态（不检查closed属性，因为它不存在）
                if hasattr(self.websocket, 'state'):
                    from websockets.protocol import State
                    if self.websocket.state in (State.CLOSED, State.CLOSING):
                        return True
        except Exception:
            # 如果检查WebSocket状态时出现异常，也认为是连接问题
            return True
            
        # 最后才检查错误消息中的关键字（作为后备机制）
        error_str = str(error).lower()
        connection_keywords = [
            'connection', 'closed', 'reset', 'refused', 'lost', 'broken',
            'network', 'unreachable', 'timeout', 'disconnected', 'socket',
            'peer', 'remote', 'frame', 'protocol', 'handshake'
        ]
        return any(keyword in error_str for keyword in connection_keywords)
                
    def stop(self):
        """停止事件循环"""
        logger.bind(tag=TAG).info(f"[{self.client_id}] 🛑 开始停止事件循环管理器")
        with self._lock:
            if not self._started or self._stopping:
                logger.bind(tag=TAG).info(f"[{self.client_id}] ⚠️ 管理器未启动或已在停止中，跳过停止操作")
                return
            self._stopping = True
            logger.bind(tag=TAG).info(f"[{self.client_id}] 🚩 设置停止标志为True")
            
        # 【CPU修复】发送毒丸信号到请求队列，让工作协程退出
        if self.loop and not self.loop.is_closed():
            try:
                if self.request_queue:
                    # 创建一个future来发送毒丸信号
                    poison_future = asyncio.Future()
                    poison_future.set_result(None)  # 设置默认结果，避免等待
                    asyncio.run_coroutine_threadsafe(
                        self.request_queue.put((None, poison_future)), 
                        self.loop
                    ).result(timeout=2)
                    logger.bind(tag=TAG).info(f"[{self.client_id}] 💊 已发送毒丸信号到请求队列")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"[{self.client_id}] 发送毒丸信号失败: {e}")
            
        if self.loop and not self.loop.is_closed():
            # 先关闭WebSocket
            try:
                asyncio.run_coroutine_threadsafe(self.close_websocket(), self.loop).result(timeout=3)
            except Exception as e:
                logger.bind(tag=TAG).warning(f"[{self.client_id}] 关闭WebSocket时出错: {e}")
            
            # 停止事件循环
            self.loop.call_soon_threadsafe(self.loop.stop)
            
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
            # 【重要修复】确保线程真正停止，防止资源泄漏
            if self.thread.is_alive():
                logger.bind(tag=TAG).error(f"[{self.client_id}] 事件循环线程未在5秒内停止，强制终止可能导致资源泄漏")
                # 如果线程仍在运行，记录详细信息用于调试
                import traceback
                if hasattr(self.thread, '_target'):
                    logger.bind(tag=TAG).error(f"[{self.client_id}] 未停止的线程目标: {self.thread._target}")
                logger.bind(tag=TAG).error(f"[{self.client_id}] 线程状态: alive={self.thread.is_alive()}, daemon={self.thread.daemon}")
                
                # 尝试强制关闭事件循环
                if self.loop and not self.loop.is_closed():
                    try:
                        # 在事件循环中安排所有任务取消
                        future = asyncio.run_coroutine_threadsafe(self._force_cleanup(), self.loop)
                        future.result(timeout=2)
                    except Exception as cleanup_e:
                        logger.bind(tag=TAG).error(f"[{self.client_id}] 强制清理失败: {cleanup_e}")
                        
        logger.bind(tag=TAG).info(f"[{self.client_id}] 事件循环管理器已停止")

    async def _force_cleanup(self):
        """清理当前客户端相关的任务，不影响其他任务"""
        try:
            # 【关键修复】只清理与当前客户端相关的任务，不要取消所有任务
            client_tasks = []
            
            # 只取消当前客户端的工作协程
            if self.worker_task and not self.worker_task.done():
                client_tasks.append(self.worker_task)
                self.worker_task.cancel()
                logger.bind(tag=TAG).info(f"[{self.client_id}] 取消客户端工作协程")
            
            # 等待客户端相关任务完成
            if client_tasks:
                try:
                    await asyncio.gather(*client_tasks, return_exceptions=True)
                    logger.bind(tag=TAG).info(f"[{self.client_id}] 已清理 {len(client_tasks)} 个客户端任务")
                except Exception as e:
                    logger.bind(tag=TAG).warning(f"[{self.client_id}] 等待客户端任务清理时出错: {e}")
            
            # 关闭WebSocket如果还存在
            if self.websocket:
                try:
                    # 检查WebSocket是否需要关闭
                    should_close = True
                    if hasattr(self.websocket, 'state'):
                        from websockets.protocol import State
                        if self.websocket.state in (State.CLOSED, State.CLOSING):
                            should_close = False
                    
                    if should_close:
                        await self.websocket.close()
                        logger.bind(tag=TAG).info(f"[{self.client_id}] WebSocket连接已关闭")
                except Exception as close_error:
                    logger.bind(tag=TAG).warning(f"[{self.client_id}] 关闭WebSocket时出错: {close_error}")
                
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{self.client_id}] 客户端清理过程中出错: {e}")

    def get_next_sequence(self) -> int:
        """获取下一个序列号（线程安全）"""
        with self._sequence_lock:
            self._sequence_counter += 1
            return self._sequence_counter


class DoubaoConnectionManager:
    """豆包TTS WebSocket连接管理器 - 为每个客户端创建独立事件循环"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        self._initialized = True
        # 客户端ID到事件循环管理器的映射
        self.client_managers: Dict[str, ClientEventLoopManager] = {}
        self.manager_lock = threading.Lock()
        # 【CPU修复】跟踪正在清理的客户端，防止为即将关闭的客户端创建新请求
        self._cleanup_in_progress: set = set()
        
    def get_client_manager(self, client_id: str) -> ClientEventLoopManager:
        """获取或创建客户端的事件循环管理器"""
        # 【CPU修复】如果客户端正在清理中，拒绝创建新的事件循环管理器
        if client_id in self._cleanup_in_progress:
            logger.bind(tag=TAG).warning(f"[{client_id}] 🚫 客户端正在清理中，拒绝创建事件循环管理器")
            raise Exception(f"Client {client_id} is being cleaned up, cannot create new manager")
        
        with self.manager_lock:
            # 再次检查（双重检查锁定模式）
            if client_id in self._cleanup_in_progress:
                logger.bind(tag=TAG).warning(f"[{client_id}] 🚫 客户端在锁内仍在清理中，拒绝创建事件循环管理器")
                raise Exception(f"Client {client_id} is being cleaned up, cannot create new manager")
                
            if client_id not in self.client_managers:
                logger.bind(tag=TAG).info(f"🏗️ 为客户端 {client_id} 创建新的事件循环管理器")
                self.client_managers[client_id] = ClientEventLoopManager(client_id, self)
            else:
                logger.bind(tag=TAG).debug(f"♻️ 复用客户端 {client_id} 的事件循环管理器")
            return self.client_managers[client_id]
            
    async def get_connection(self, access_token: str, cluster: str, config: dict = None, client_id: str = None) -> Optional[websockets.WebSocketServerProtocol]:
        """获取WebSocket连接 - 在客户端专属的事件循环中"""
        if not client_id:
            client_id = "default"
            
        manager = self.get_client_manager(client_id)
        
        # 如果已有连接且健康，直接返回
        if manager.websocket:
            try:
                # 测试连接是否健康
                pong_waiter = await manager.websocket.ping()
                await asyncio.wait_for(pong_waiter, timeout=5)
                logger.bind(tag=TAG).debug(f"[{client_id}] 复用现有WebSocket连接")
                return manager.websocket
            except Exception as e:
                logger.bind(tag=TAG).warning(f"[{client_id}] 连接健康检查失败: {e}")
                await manager.close_websocket()
                
        # 创建新连接
        try:
            websocket = await manager.create_websocket(access_token, cluster, config)
            return websocket
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{client_id}] 创建WebSocket连接失败: {e}")
            return None
            
    async def close_client_connections(self, client_id: str):
        """关闭指定客户端的连接和事件循环"""
        # 【CPU修复】标记客户端正在清理，防止新的TTS请求
        self._cleanup_in_progress.add(client_id)
        logger.bind(tag=TAG).info(f"[{client_id}] 🔄 标记客户端开始清理，阻止新的TTS请求")
        logger.bind(tag=TAG).info(f"[{client_id}] 🔍 当前清理列表: {self._cleanup_in_progress}")
        logger.bind(tag=TAG).info(f"[{client_id}] 🔍 当前客户端管理器: {list(self.client_managers.keys())}")

        try:
            with self.manager_lock:
                if client_id in self.client_managers:
                    manager = self.client_managers.pop(client_id)
                    logger.bind(tag=TAG).info(f"[{client_id}] 🛑 找到客户端管理器，开始停止...")
                    manager.stop()
                    logger.bind(tag=TAG).info(f"已关闭客户端 {client_id} 的TTS连接和事件循环")
                else:
                    logger.bind(tag=TAG).warning(f"[{client_id}] ⚠️ 客户端管理器不存在，可能已经被清理")
        finally:
            # 清理完成后从清理列表中移除
            self._cleanup_in_progress.discard(client_id)
            logger.bind(tag=TAG).info(f"[{client_id}] ✅ 客户端清理完成，移除清理标记")
                
    async def close_all_connections(self):
        """关闭所有连接"""
        with self.manager_lock:
            client_ids = list(self.client_managers.keys())
            for client_id in client_ids:
                manager = self.client_managers.pop(client_id)
                manager.stop()
        logger.bind(tag=TAG).info("已关闭所有TTS连接和事件循环")


class TTSProvider(TTSProviderBase):
    # 类级别的连接管理器
    _connection_manager: ClassVar[DoubaoConnectionManager] = None
    
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        if config.get("appid"):
            self.appid = int(config.get("appid"))
        else:
            self.appid = ""
        self.access_token = config.get("access_token")
        self.cluster = config.get("cluster")
        # 保存完整配置以便传递给连接管理器
        self.config = config

        if config.get("private_voice"):
            self.voice = config.get("private_voice")
        else:
            self.voice = config.get("voice")

        # 处理空字符串的情况
        speed_ratio = config.get("speed_ratio", "1.0")
        volume_ratio = config.get("volume_ratio", "1.0")
        pitch_ratio = config.get("pitch_ratio", "1.0")

        self.speed_ratio = float(speed_ratio) if speed_ratio else 1.0
        self.volume_ratio = float(volume_ratio) if volume_ratio else 1.0
        self.pitch_ratio = float(pitch_ratio) if pitch_ratio else 1.0

        # 获取请求超时时间配置
        self.request_timeout = float(config.get("request_timeout", 10.0))

        # 初始化连接管理器
        if TTSProvider._connection_manager is None:
            TTSProvider._connection_manager = DoubaoConnectionManager()
        
        check_model_key("TTS", self.access_token)

    def generate_filename(self, extension=".wav"):
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    def create_binary_message(self, payload: bytes) -> bytes:
        """
        创建二进制协议消息（根据官方Demo）
        
        Args:
            payload: JSON负载的字节数据
            
        Returns:
            完整的二进制消息
        """
        # 根据官方Demo的实现：
        # version: b0001 (4 bits)
        # header size: b0001 (4 bits) 
        # message type: b0001 (Full client request) (4bits)
        # message type specific flags: b0000 (none) (4bits)
        # message serialization method: b0001 (JSON) (4 bits)
        # message compression: b0001 (gzip) (4bits)
        # reserved data: 0x00 (1 byte)
        default_header = bytearray(b'\x11\x10\x11\x00')
        
        # 使用gzip压缩payload
        compressed_payload = gzip.compress(payload)
        
        # 构建完整消息
        full_client_request = bytearray(default_header)
        full_client_request.extend((len(compressed_payload)).to_bytes(4, 'big'))  # payload size(4 bytes)
        full_client_request.extend(compressed_payload)  # payload
        
        # 检查最终消息大小
        final_message_size = len(full_client_request)
        logger.bind(tag=TAG).debug(f"WebSocket发送消息: 头部={default_header.hex()}, 压缩前={len(payload)}, 压缩后={len(compressed_payload)}, 最终消息={final_message_size}字节")
        
        # 如果消息异常大，输出详细信息进行调试
        if final_message_size > 100000:  # 100KB就算异常大
            logger.bind(tag=TAG).warning(f"检测到异常大的消息: {final_message_size}字节")
            logger.bind(tag=TAG).warning(f"原始payload前1000字符: {payload[:1000]}")
            logger.bind(tag=TAG).warning(f"压缩后前1000字节hex: {compressed_payload[:1000].hex()}")
        
        # 检查是否超过WebSocket帧大小限制（通常是1MB）
        max_frame_size = 1048576  # 1MB
        if final_message_size > max_frame_size:
            logger.bind(tag=TAG).error(f"消息过大: {final_message_size}字节 > {max_frame_size}字节限制")
            logger.bind(tag=TAG).error(f"原始payload: {payload}")
            raise Exception(f"消息过大: {final_message_size}字节 > {max_frame_size}字节限制，文本长度: {len(payload.decode('utf-8', errors='ignore'))}字符")
        
        return bytes(full_client_request)

    async def text_to_speak(self, text, output_file, client_id: str = None):
        """使用WebSocket进行语音合成 - 在客户端专属事件循环中执行"""
        if not client_id:
            client_id = "default"
        
        # 【CPU修复】检查客户端是否已经在清理列表中，避免为已断开的客户端创建新的事件循环
        # 这是防止CPU占用率100%问题的关键检查
        if client_id in getattr(self._connection_manager, '_cleanup_in_progress', set()):
            logger.bind(tag=TAG).warning(f"[{client_id}] 🚫 客户端正在清理中，跳过TTS请求: text='{text[:30]}...'")
            return
        
        # 【重要修复】为了避免声音配置混淆，将当前实例的声音配置传递给请求处理函数
        # 这样确保每个请求都使用正确的用户声音配置，而不依赖实例的共享状态
        voice_config = {
            "voice": self.voice,
            "speed_ratio": self.speed_ratio,
            "volume_ratio": self.volume_ratio,
            "pitch_ratio": self.pitch_ratio
        }
            
        # 【声音配置日志1】记录当前实例的声音配置
        logger.bind(tag=TAG).info(f"🎤 开始TTS合成请求: client_id={client_id}, text='{text[:50]}...', 当前实例声音配置={self.voice}")
        logger.bind(tag=TAG).info(f"[{client_id}] 🔊 TTS实例详细配置: voice={self.voice}, speed={self.speed_ratio}, volume={self.volume_ratio}, pitch={self.pitch_ratio}")
        logger.bind(tag=TAG).info(f"[{client_id}] 🔒 传递到请求的声音配置: {voice_config}")
            
        # 获取客户端的事件循环管理器
        try:
            manager = self._connection_manager.get_client_manager(client_id)
            logger.bind(tag=TAG).debug(f"[{client_id}] 📋 获取到事件循环管理器")
        except Exception as e:
            logger.bind(tag=TAG).warning(f"[{client_id}] 🚫 无法获取事件循环管理器（可能正在清理）: {e}")
            return
        
        # 确保管理器已启动
        if not manager._started:
            manager.start()
            
            # 等待WebSocket连接建立
            try:
                # 直接在客户端事件循环中创建连接
                manager.run_coroutine(
                    manager.create_websocket(self.access_token, self.cluster, self.config)
                )
            except Exception as e:
                logger.bind(tag=TAG).error(f"[{client_id}] 创建WebSocket连接失败: {e}")
                raise Exception(f"无法建立WebSocket连接: {e}")
        
        sequence_id = manager.get_next_sequence()

        # 【重要修复】使用传递的声音配置而不是实例属性，避免多用户间的声音混淆
        # 构建请求JSON
        request_data = {
            "app": {
                "appid": self.appid,
                "token": self.access_token,
                "cluster": self.cluster,
            },
            "user": {
                "uid": client_id or "yuyan_server_user"
            },
            "audio": {
                "voice_type": voice_config["voice"],  # 使用传递的声音配置
                "encoding": "wav",
                "speed_ratio": voice_config["speed_ratio"],  # 使用传递的声音配置
                "volume_ratio": voice_config["volume_ratio"],  # 使用传递的声音配置
                "pitch_ratio": voice_config["pitch_ratio"],  # 使用传递的声音配置
            },
            "request": {
                "reqid": f"{client_id or 'default'}_{sequence_id}_{uuid.uuid4().hex[:8]}",
                "text": text,
                "operation": "submit",
            }
        }
        
        # 【声音配置日志2】记录即将发送给豆包API的声音配置
        logger.bind(tag=TAG).info(f"[{client_id}][seq:{sequence_id}] 🎵 即将发送给豆包API的voice_type={request_data['audio']['voice_type']}")
        logger.bind(tag=TAG).info(f"[{client_id}][seq:{sequence_id}] 🔍 音频配置验证: voice_type={request_data['audio']['voice_type']}, speed={request_data['audio']['speed_ratio']}, volume={request_data['audio']['volume_ratio']}, pitch={request_data['audio']['pitch_ratio']}")
        
        # 检查各字段大小
        logger.bind(tag=TAG).debug(f"[{client_id}][seq:{sequence_id}] 请求字段大小检查:")
        logger.bind(tag=TAG).debug(f"  - appid: {len(str(self.appid))} chars")
        logger.bind(tag=TAG).debug(f"  - token: {len(str(self.access_token))} chars")
        logger.bind(tag=TAG).debug(f"  - cluster: {len(str(self.cluster))} chars")
        logger.bind(tag=TAG).debug(f"  - voice_type: {len(str(voice_config['voice']))} chars")
        logger.bind(tag=TAG).debug(f"  - text: {len(text)} chars: '{text}'")
        logger.bind(tag=TAG).debug(f"  - uid: {len(client_id or 'yuyan_server_user')} chars")

        # 转换为JSON字节
        json_payload = json.dumps(request_data, ensure_ascii=True).encode('utf-8')
        logger.bind(tag=TAG).debug(f"[{client_id}][seq:{sequence_id}] JSON负载大小: {len(json_payload)}字节")
        logger.bind(tag=TAG).debug(f"[{client_id}][seq:{sequence_id}] 完整请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        # 创建二进制消息
        binary_message = self.create_binary_message(json_payload)
        logger.bind(tag=TAG).debug(f"[{client_id}][seq:{sequence_id}] 二进制消息总大小: {len(binary_message)}字节")

        # 准备请求数据
        request = {
            "text": text,
            "output_file": output_file,
            "sequence_id": sequence_id,
            "binary_message": binary_message,
            "timeout": self.request_timeout,
            "voice_config": voice_config  # 【重要修复】将声音配置传递给请求处理函数
        }

        try:
            logger.bind(tag=TAG).info(f"[{client_id}][seq:{sequence_id}] 🚀 提交TTS请求到专属事件循环")
            # 直接在客户端事件循环中提交请求并等待结果
            result = manager.run_coroutine(manager.submit_request(request))
            if not result:
                raise Exception("TTS请求处理失败")
            logger.bind(tag=TAG).info(f"[{client_id}][seq:{sequence_id}] 🎉 TTS合成完全完成")
                 
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{client_id}][seq:{sequence_id}] 💥 TTS合成失败: {e}")
            raise Exception(f"{__name__} error: {e}")

    @classmethod
    async def close_all_connections(cls):
        """关闭所有WebSocket连接"""
        if cls._connection_manager:
            await cls._connection_manager.close_all_connections()

    @classmethod
    async def close_client_connections(cls, client_id: str):
        """关闭指定客户端的WebSocket连接"""
        if cls._connection_manager:
            await cls._connection_manager.close_client_connections(client_id)

    def __del__(self):
        """析构函数，清理资源"""
        # 注意：不在这里关闭连接，因为连接是共享的
        pass
