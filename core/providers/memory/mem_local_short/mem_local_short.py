from typing import List, Dict
from ..base import MemoryProviderBase, logger
import time
import json
from config.config_loader import load_config
from core.utils.redis_client import RedisClient


def extract_json_data(json_code):
    """
    简化的JSON提取函数，期望LLM直接输出合法JSON
    仅在LLM不支持JSON Schema时作为后备方案使用
    """
    if not json_code:
        return ""
    
    # 移除无效的控制字符
    import re
    cleaned_code = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_code.strip())
    
    # 如果直接是有效JSON，返回
    try:
        json.loads(cleaned_code)
        return cleaned_code
    except:
        pass
    
    # 尝试寻找JSON对象边界
    start = cleaned_code.find("{")
    end = cleaned_code.rfind("}")
    
    if start != -1 and end != -1 and end > start:
        potential_json = cleaned_code[start:end + 1]
        try:
            json.loads(potential_json)
            return potential_json
        except:
            pass
    
    return ""


# 提示词模板
user_profile_extraction_prompt = """
你是一个专业的用户信息提取助手。请从对话历史中提取关键的用户档案信息和偏好设置。

## 核心任务
从对话中提取关于用户的关键信息，输出为JSON格式。

## 提取原则
1. **事实优先**：只提取明确提到的客观事实（姓名、职业、地点等）
2. **用户中心**：专注于用户相关信息，忽略AI助手自身状态
3. **长期价值**：优先提取不会频繁变化的信息
4. **简洁明确**：用最简短的短语表达

## 输出要求
严格按照以下JSON Schema格式输出：

```json
{
  "user_profile": {
    "name": "用户姓名或昵称",
    "age": "年龄信息",
    "gender": "性别",
    "occupation": "职业",
    "location": "地点信息",
    "lifestyle": "生活方式",
    "interests": ["兴趣爱好1", "兴趣爱好2"],
    "important_dates": "重要日期信息"
  },
  "preferences": {
    "communication_style": "沟通风格偏好",
    "special_requests": "特殊要求"
  }
}
```

请基于以下对话提取用户信息：
"""


# 对话总结提示词
dialogue_summary_prompt = """
# 对话历史总结助手

## 核心任务
将多条对话压缩为简洁的总结，重点描述Assistant和User分别说了什么。

## 总结原则
1. **内容保留**：保留对话的核心内容和上下文
2. **角色明确**：明确标注Assistant说了什么，User说了什么
3. **简洁准确**：用简练的语言概括要点

## 输出要求
严格按照以下JSON Schema格式输出：

```json
{
  "assistant_summary": "总结助手的回应内容",
  "user_summary": "总结用户的发言内容"
}
```

请基于以下对话历史生成总结：
"""

# 对话总结的JSON Schema
dialogue_summary_schema = {
    "type": "object",
    "required": ["assistant_summary", "user_summary"],
    "properties": {
        "assistant_summary": {"type": "string"},
        "user_summary": {"type": "string"}
    }
}


TAG = __name__


class MemoryProvider(MemoryProviderBase):
    def __init__(self, config):
        super().__init__(config)
        self.short_momery = ""
        
        # 初始化 Redis 客户端，使用全局配置
        try:
            app_config = load_config()
            self.redis_client = RedisClient(app_config)
            
            if not self.redis_client.enabled or not self.redis_client.client:
                raise ConnectionError("Redis memory storage is required but not available")
                
            logger.bind(tag=TAG).info("Redis 记忆存储初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Redis 记忆存储初始化失败: {str(e)}")
            raise

    def init_memory(self, role_id, llm):
        super().init_memory(role_id, llm)
        self.load_memory()

    def load_memory(self):
        """从Redis加载聊天历史记录"""
        if not self.role_id:
            logger.bind(tag=TAG).warning("role_id未设置，无法加载记忆")
            self.short_momery = ""
            return
            
        try:
            memory_data = self.redis_client.get_memory(self.role_id)
            self.short_momery = memory_data or ""
            logger.bind(tag=TAG).info(f"Memory loaded from Redis - Role: {self.role_id}, Data: {'Found' if memory_data else 'Empty'}")
        except Exception as e:
            logger.bind(tag=TAG).error(f"从Redis加载记忆失败: {e}")
            self.short_momery = ""
            raise

    def save_memory_to_storage(self):
        """保存记忆到Redis"""
        if not self.role_id:
            logger.bind(tag=TAG).error("role_id未设置，无法保存记忆")
            raise ValueError("role_id is required for saving memory")
            
        try:
            success = self.redis_client.save_memory(self.role_id, self.short_momery)
            if success:
                logger.bind(tag=TAG).info(f"Memory saved to Redis - Role: {self.role_id}")
            else:
                logger.bind(tag=TAG).error(f"Failed to save memory to Redis - Role: {self.role_id}")
                raise Exception("Redis保存失败")
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存记忆到Redis失败: {e}")
            raise

    async def save_memory(self, msgs):
        """
        保存聊天历史的核心方法 - 重构版本
        新规则：只保存摘要，不保存原始对话记录
        """
        if self.llm is None:
            logger.bind(tag=TAG).error("LLM is not set for memory provider")
            return None

        if len(msgs) < 2:
            logger.bind(tag=TAG).debug("消息数量不足，跳过记忆保存")
            return None

        logger.bind(tag=TAG).info(f"开始保存记忆 - Role: {self.role_id}, 消息数量: {len(msgs)}")

        # 提取有效的对话消息（排除system role）
        valid_messages = []
        filtered_system_count = 0
        
        for msg in msgs:
            if not hasattr(msg, 'role') or not hasattr(msg, 'content'):
                logger.bind(tag=TAG).debug(f"跳过无效消息对象")
                continue
            if msg.content is None or msg.content == "":
                logger.bind(tag=TAG).debug(f"跳过空内容消息: role={msg.role}")
                continue
            # 排除system role的消息
            if msg.role == "system":
                filtered_system_count += 1
                logger.bind(tag=TAG).info(f"✓ 过滤system消息 #{filtered_system_count}")
                continue
            
            try:
                content_str = str(msg.content).strip()
                if "object at 0x" in content_str or content_str.startswith("<") and content_str.endswith(">"):
                    logger.bind(tag=TAG).debug(f"跳过对象引用消息")
                    continue
                valid_messages.append({"role": msg.role, "content": content_str})
            except Exception as e:
                logger.bind(tag=TAG).warning(f"处理消息时出错: {e}")
                continue

        logger.bind(tag=TAG).info(f"过滤结果: 原始消息={len(msgs)}条, 过滤system消息={filtered_system_count}条, 有效消息={len(valid_messages)}条")

        if len(valid_messages) < 2:
            logger.bind(tag=TAG).debug("有效消息数量不足，跳过记忆保存")
            return None

        # 获取现有摘要
        existing_summary = ""
        if self.short_momery:
            try:
                existing_data = json.loads(self.short_momery)
                existing_summary = existing_data.get("summary", "")
                logger.bind(tag=TAG).info(f"现有摘要长度: {len(existing_summary)}字符")
            except json.JSONDecodeError:
                logger.bind(tag=TAG).warning("现有记忆格式无效，重新开始")

        # 生成当前对话的摘要
        current_summary = await self._summarize_messages(valid_messages)
        if not current_summary:
            logger.bind(tag=TAG).warning("当前对话摘要生成失败")
            return None

        # 合并摘要：如果有现有摘要，则合并；否则直接使用当前摘要
        final_summary = ""
        if existing_summary:
            # 合并现有摘要和当前摘要
            final_summary = await self._merge_summaries(existing_summary, current_summary)
        else:
            final_summary = current_summary

        if not final_summary:
            logger.bind(tag=TAG).warning("最终摘要生成失败")
            return None

        # 构建最终的记忆数据（只包含摘要）
        memory_data = {
            "summary": final_summary,
            "last_update_time": time.time()
        }

        # 保存记忆
        self.short_momery = json.dumps(memory_data, ensure_ascii=False)
        
        logger.bind(tag=TAG).info(f"构建的最终记忆数据: 摘要长度={len(final_summary)}字符")
        
        self.save_memory_to_storage()

        # 提取并保存用户档案信息
        await self._extract_and_save_user_profile(valid_messages)

        logger.bind(tag=TAG).info(f"记忆保存完成 - Role: {self.role_id}, 最终记忆数据长度: {len(self.short_momery)} 字符")
        return self.short_momery

    async def _summarize_messages(self, messages):
        """总结对话消息"""
        if not messages:
            logger.bind(tag=TAG).debug("没有消息需要总结")
            return ""
        
        logger.bind(tag=TAG).info(f"开始总结对话消息: 消息数量={len(messages)}, 消息内容={json.dumps(messages, ensure_ascii=False, indent=2)}")
        
        dialogue_text = ""
        for msg in messages:
            # 排除system消息，只处理user和assistant消息
            if msg["role"] == "user":
                dialogue_text += f"User: {msg['content']}\n"
            elif msg["role"] == "assistant":
                dialogue_text += f"Assistant: {msg['content']}\n"
            # system消息被忽略

        logger.bind(tag=TAG).info(f"构建的对话文本用于总结: {dialogue_text}")

        try:
            # 优先使用LLM的JSON Schema功能
            if hasattr(self.llm, 'response_with_schema'):
                logger.bind(tag=TAG).debug("使用LLM的JSON Schema功能进行对话总结")
                result = self.llm.response_with_schema(dialogue_summary_prompt, dialogue_text, dialogue_summary_schema)
                
                # 如果LLM支持JSON Schema，解析并格式化结果
                if isinstance(result, str):
                    summary_data = json.loads(result)
                else:
                    summary_data = result
                
                # 格式化为文本
                assistant_summary = summary_data.get("assistant_summary", "")
                user_summary = summary_data.get("user_summary", "")
                summary = f"Assistant说了什么：{assistant_summary}\nUser说了什么：{user_summary}"
                logger.bind(tag=TAG).info(f"JSON Schema总结结果: {json.dumps(summary_data, ensure_ascii=False, indent=2)}, 格式化后的总结: {summary}")
            else:
                # 回退到普通调用
                logger.bind(tag=TAG).debug("LLM不支持JSON Schema，使用普通调用进行对话总结")
                summary = self.llm.response_no_stream(dialogue_summary_prompt, dialogue_text)
                logger.bind(tag=TAG).info(f"普通调用总结结果: {summary}")
            
            logger.bind(tag=TAG).info(f"对话总结完成，原始消息数: {len(messages)}, 最终总结: {summary}")
            return summary
        except Exception as e:
            logger.bind(tag=TAG).error(f"对话总结失败: {e}")
            return ""

    async def _merge_summaries(self, existing_summary, current_summary):
        """合并现有摘要和当前对话摘要"""
        if not existing_summary:
            return current_summary
        if not current_summary:
            return existing_summary
            
        logger.bind(tag=TAG).info(f"开始合并摘要: 现有摘要长度={len(existing_summary)}, 当前摘要长度={len(current_summary)}")
        
        # 构建合并内容
        merge_content = f"历史摘要：{existing_summary}\n\n新增对话摘要：{current_summary}"
        
        try:
            # 使用专门的摘要合并提示词
            merge_prompt = """
# 对话摘要合并助手

## 核心任务
将提供的历史摘要和新增对话摘要合并为一个完整的总结，保持简洁明了。

## 合并原则
1. **信息整合**：合并历史信息和新增信息
2. **去除重复**：避免重复表达相同内容
3. **重点突出**：保留关键信息，简化次要细节
4. **逻辑清晰**：按照对话发展的时间顺序组织内容

## 输出要求
严格按照以下JSON Schema格式输出：

```json
{
  "assistant_summary": "总结助手在整个对话历史中的所有回应内容",
  "user_summary": "总结用户在整个对话历史中的所有发言内容"
}
```

请基于以下内容生成合并摘要：
"""
            
            # 优先使用LLM的JSON Schema功能
            if hasattr(self.llm, 'response_with_schema'):
                logger.bind(tag=TAG).debug("使用LLM的JSON Schema功能进行摘要合并")
                result = self.llm.response_with_schema(merge_prompt, merge_content, dialogue_summary_schema)
                
                # 如果LLM支持JSON Schema，解析并格式化结果
                if isinstance(result, str):
                    summary_data = json.loads(result)
                else:
                    summary_data = result
                
                # 格式化为文本
                assistant_summary = summary_data.get("assistant_summary", "")
                user_summary = summary_data.get("user_summary", "")
                merged_summary = f"Assistant说了什么：{assistant_summary}\nUser说了什么：{user_summary}"
                logger.bind(tag=TAG).info(f"JSON Schema摘要合并结果: 最终摘要长度={len(merged_summary)}")
            else:
                # 回退到普通调用
                logger.bind(tag=TAG).debug("LLM不支持JSON Schema，使用普通调用进行摘要合并")
                merged_summary = self.llm.response_no_stream(merge_prompt, merge_content)
                logger.bind(tag=TAG).info(f"普通调用摘要合并结果: 最终摘要长度={len(merged_summary)}")
            
            logger.bind(tag=TAG).info(f"摘要合并完成")
            return merged_summary
        except Exception as e:
            logger.bind(tag=TAG).error(f"摘要合并失败: {e}")
            # 合并失败时返回简单拼接
            return f"{existing_summary}\n{current_summary}"


    async def _extract_and_save_user_profile(self, messages):
        """提取用户档案信息并保存到UserProfileManager"""
        try:
            # 构建消息文本（排除system消息）
            dialogue_text = ""
            for msg in messages:
                # 只处理user和assistant消息，排除system消息
                if msg["role"] == "user":
                    dialogue_text += f"User: {msg['content']}\n"
                elif msg["role"] == "assistant":
                    dialogue_text += f"Assistant: {msg['content']}\n"
                # system消息被忽略

            # 添加当前时间
            time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            dialogue_text += f"当前时间：{time_str}"

            # 定义JSON Schema
            json_schema = {
                "type": "object",
                "required": ["user_profile", "preferences"],
                "properties": {
                    "user_profile": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "age": {"type": "string"},
                            "gender": {"type": "string"},
                            "occupation": {"type": "string"},
                            "location": {"type": "string"},
                            "lifestyle": {"type": "string"},
                            "interests": {"type": "array", "items": {"type": "string"}},
                            "important_dates": {"type": "string"}
                        }
                    },
                    "preferences": {
                        "type": "object",
                        "properties": {
                            "communication_style": {"type": "string"},
                            "special_requests": {"type": "string"}
                        }
                    }
                }
            }

            # 优先使用LLM的JSON Schema功能
            if hasattr(self.llm, 'response_with_schema'):
                logger.bind(tag=TAG).debug("使用LLM的JSON Schema功能提取用户档案")
                result = self.llm.response_with_schema(user_profile_extraction_prompt, dialogue_text, json_schema)
                
                # 如果LLM支持JSON Schema，直接解析结果
                if isinstance(result, str):
                    profile_data = json.loads(result)
                else:
                    profile_data = result
            else:
                # 回退到普通调用 + 手动解析
                logger.bind(tag=TAG).debug("LLM不支持JSON Schema，使用普通调用+手动解析")
                result = self.llm.response_no_stream(user_profile_extraction_prompt, dialogue_text)
                
                if not result:
                    logger.bind(tag=TAG).debug("LLM未返回用户档案提取结果")
                    return

                # 使用extract_json_data提取JSON
                json_str = extract_json_data(result)
                if not json_str:
                    logger.bind(tag=TAG).debug("无法提取有效的用户档案JSON")
                    return

                profile_data = json.loads(json_str)

            user_profile = profile_data.get("user_profile", {})
            preferences = profile_data.get("preferences", {})

            # 这里需要获取UserProfileManager实例来保存用户档案
            # 注意：由于当前架构限制，我们暂时通过日志记录提取的信息
            # 在实际使用中，需要将UserProfileManager实例传递给MemoryProvider
            
            logger.bind(tag=TAG).info(f"提取用户档案信息: {json.dumps(user_profile, ensure_ascii=False)}")
            logger.bind(tag=TAG).info(f"提取用户偏好设置: {json.dumps(preferences, ensure_ascii=False)}")
            
            # TODO: 调用UserProfileManager保存用户档案信息
            # user_profile_manager.save_user_profile(user_profile, preferences)
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"提取用户档案信息失败: {e}")

    async def query_memory(self, query: str) -> str:
        """
        查询聊天历史的核心方法 - 重构版本
        只返回摘要内容，不返回原始对话
        """
        # 实时从Redis获取最新的记忆数据
        if not self.role_id:
            logger.bind(tag=TAG).warning("role_id未设置，无法查询记忆")
            return ""

        try:
            latest_memory = self.redis_client.get_memory(self.role_id) or ""
            logger.bind(tag=TAG).info(f"从Redis实时获取记忆数据: {len(latest_memory)} 字符")
        except Exception as e:
            logger.bind(tag=TAG).error(f"从Redis获取记忆失败: {e}")
            return ""

        if not latest_memory:
            logger.bind(tag=TAG).info("Redis中没有找到记忆数据，返回空摘要")
            return ""

        try:
            memory_data = json.loads(latest_memory)
            summary = memory_data.get("summary", "")
            last_update_time = memory_data.get("last_update_time", "未知")

            logger.bind(tag=TAG).info(f"解析记忆数据: 摘要长度={len(summary)}字符, 最后更新时间={last_update_time}")
            
            return summary

        except json.JSONDecodeError:
            logger.bind(tag=TAG).warning("记忆数据格式错误，返回空摘要")
            return ""
        except Exception as e:
            logger.bind(tag=TAG).error(f"查询记忆失败: {e}")
            return ""