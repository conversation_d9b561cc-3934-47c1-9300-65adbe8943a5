import os
import copy
import json
import subprocess
import sys
import uuid
import time
import queue
import asyncio
import traceback
import re

import threading
import websockets
from typing import Dict, Any
from plugins_func.loadplugins import auto_import_modules
from core.providers.llm.base import mark_session_closed, cleanup_session
from config.logger import setup_logging
from config.role_loader import get_role_loader
from core.utils.dialogue import Message, Dialogue, filter_sensitive_info, segment_text_with_merge, find_text_split_point
from core.policy_check import PolicyCheck
from core.handle.textHandle import handleTextMessage
from core.utils.util import (
    get_string_no_punctuation_or_emoji,
    extract_json_from_string,
    initialize_modules,
    check_vad_update,
    check_asr_update,
    fix_tool_call_arguments,
)
from concurrent.futures import TimeoutError
from core.handle.sendAudioHandle import sendAudioMessage
from core.handle.receiveAudioHandle import handleAudioMessage
from core.handle.functionHandler import FunctionHandler
from plugins_func.register import Action, ActionResponse
from core.auth import AuthMiddleware, AuthenticationError
from core.mcp.manager import MCPManager
from config.config_loader import get_private_config_from_api
from config.manage_api_client import DeviceNotFoundException, DeviceBindException
from core.utils.output_counter import add_device_output
from core.handle.reportHandle import enqueue_tts_report, report
from core.utils.latency_tracker import LatencyTracker
from core.utils.thread_pool import DaemonThreadPoolExecutor

TAG = __name__

auto_import_modules("plugins_func.functions")


class TTSException(RuntimeError):
    pass


class ConnectionHandler:
    def __init__(
        self,
        config: Dict[str, Any],
        _vad,
        _asr,
        _llm,
        _tts,
        _memory,
        _intent,
        server=None,
    ):
        self.common_config = config
        self.config = copy.deepcopy(config)
        self.session_id = str(uuid.uuid4())
        self.logger = setup_logging()
        self.server = server  # 保存server实例的引用

        self.auth = AuthMiddleware(config)
        self.need_bind = False
        self.bind_code = None
        self.read_config_from_api = self.config.get("read_config_from_api", False)
        
        # 初始化Redis客户端用于用户数据管理
        from core.utils.redis_client import RedisClient
        try:
            self.redis_client = RedisClient(config)
        except Exception as e:
            self.logger.bind(tag=TAG).warning(f"Redis客户端初始化失败: {e}")
            self.redis_client = None

        self.websocket = None
        self.headers = None
        self.device_id = None
        self.client_id = None
        self.client_ip = None
        self.client_ip_info = {}
        self.prompt = None
        self.welcome_msg = None
        self.max_output_size = 0
        self.chat_history_conf = 0

        # 角色加载器
        self.role_loader = get_role_loader()
        
        # 用户和聊天管理
        from .user_profile_manager import UserProfileManager
        self.user_profile_manager = UserProfileManager(self)

        # 客户端状态相关
        self.client_abort = False
        self.client_listen_mode = "auto"
        self.meeting_mode = False

        # 线程任务相关
        self.loop = asyncio.get_event_loop()
        self.stop_event = threading.Event()
        self.tts_queue = queue.Queue()
        self.audio_play_queue = queue.Queue()
        self.executor = DaemonThreadPoolExecutor(max_workers=10)

        # 上报线程
        self.report_queue = queue.Queue()
        self.report_thread = None
        # TODO(haotian): 2025/5/12 可以通过修改此处，调节asr的上报和tts的上报
        self.report_asr_enable = self.read_config_from_api
        self.report_tts_enable = self.read_config_from_api

        # 依赖的组件
        self.vad = None
        self.asr = None
        self._asr = _asr
        self._vad = _vad
        self.llm = _llm
        self.tts = _tts
        self.memory = _memory
        self.intent = _intent
        
        # 初始化内容安全检测
        self.policy_check = PolicyCheck(config)
        self.content_blocked = False  # 标记当前回复是否被内容安全拦截
        self.processing_redirect_response = False  # 标记是否正在处理引导性回复

        # vad相关变量
        self.client_audio_buffer = bytearray()
        self.client_have_voice = False
        self.client_have_voice_last_time = 0.0
        self.client_voice_stop = False

        # asr相关变量
        self.asr_audio = []
        self.asr_server_receive = True

        # llm相关变量
        self.llm_finish_task = False
        # 从配置中读取历史消息限制
        max_context_tokens = int(self.config.get("max_context_tokens", 4000))
        self.dialogue = Dialogue(max_context_tokens=max_context_tokens)
        self.max_context_tokens = max_context_tokens
        # 从配置中读取总token限制
        self.max_total_tokens = int(self.config.get("max_total_tokens", 524288))

        # tts相关变量
        self.tts_first_text_index = -1
        self.tts_last_text_index = -1

        # iot相关变量
        self.iot_descriptors = {}
        self.func_handler = None

        self.cmd_exit = self.config["exit_commands"]
        self.max_cmd_length = 0
        for cmd in self.cmd_exit:
            if len(cmd) > self.max_cmd_length:
                self.max_cmd_length = len(cmd)

        # 是否在聊天结束后关闭连接
        self.close_after_chat = False
        self.load_function_plugin = False
        self.intent_type = "nointent"

        # 连接状态标志
        self.is_connection_closed = False
        
        # 延迟跟踪器
        self.latency_tracker = LatencyTracker()
        self.audio_format = "opus"

    async def handle_connection(self, ws):
        try:
            # 记录连接开始时间 - 移到最前面，确保在任何可能的异常之前设置
            self.connection_start_time = time.time()

            # 获取并验证headers
            self.headers = dict(ws.request.headers)

            if self.headers.get("device-id", None) is None:
                # 尝试从 URL 的查询参数中获取 device-id
                from urllib.parse import parse_qs, urlparse

                # 从 WebSocket 请求中获取路径
                request_path = ws.request.path
                if not request_path:
                    self.logger.bind(tag=TAG).error("无法获取请求路径")
                    return
                parsed_url = urlparse(request_path)
                query_params = parse_qs(parsed_url.query)
                if "device-id" in query_params:
                    self.headers["device-id"] = query_params["device-id"][0]
                    self.headers["client-id"] = query_params["client-id"][0]
                else:
                    await ws.send("端口正常，如需测试连接，请使用test_page.html")
                    await self.close(ws)
                    return
            # 获取客户端ip地址
            self.client_ip = ws.remote_address[0]
            self.logger.bind(tag=TAG).info(
                f"{self.client_ip} conn - Headers: {self.headers}"
            )

            # 进行认证
            await self.auth.authenticate(self.headers)

            # 认证通过,继续处理
            self.websocket = ws
            self.device_id = self.headers.get("device-id", None)
            self.client_id = self.headers.get("client-id", self.device_id)  # 使用 device-id 作为备选

            # 加载用户的角色和助手名字设置
            self.logger.bind(tag=TAG).info(f"开始加载用户配置: client_id={self.client_id}")
            await self._load_user_profile()
            self.logger.bind(tag=TAG).info(f"用户配置加载完成")


            # 移除超时检查任务，不再主动断开连接

            self.welcome_msg = self.config["xiaozhi"]
            self.welcome_msg["session_id"] = self.session_id
            await self.websocket.send(json.dumps(self.welcome_msg))

            # 获取差异化配置
            self._initialize_private_config()
            # 异步初始化
            self.executor.submit(self._initialize_components)
            # tts 消化线程
            self.tts_priority_thread = threading.Thread(
                target=self._tts_priority_thread, daemon=True
            )
            self.tts_priority_thread.start()

            # 音频播放 消化线程
            self.audio_play_priority_thread = threading.Thread(
                target=self._audio_play_priority_thread, daemon=True
            )
            self.audio_play_priority_thread.start()

            try:
                self.logger.bind(tag=TAG).info("开始处理客户端消息")
                async for message in self.websocket:
                    await self._route_message(message)
                self.logger.bind(tag=TAG).info("消息队列已退出")
            except websockets.exceptions.ConnectionClosed:
                self.logger.bind(tag=TAG).info("客户端断开连接")

        except AuthenticationError as e:
            self.logger.bind(tag=TAG).error(f"Authentication failed: {str(e)}")
            return
        except Exception as e:
            stack_trace = traceback.format_exc()
            self.logger.bind(tag=TAG).error(f"Connection error: {str(e)}-{stack_trace}")
            return
        finally:
            await self._save_and_close(ws)

    async def _load_user_profile(self):
        """加载用户的角色、助手名字和声音设置等个人档案信息"""
        await self.user_profile_manager.load_user_profile()

    def _apply_role_config(self, user_data=None):
        """应用当前角色配置，处理不同场景（初次见面、再次见面等）"""
        # If user_data is not provided, try to get it from Redis
        if user_data is None and self.redis_client and self.redis_client.enabled and self.client_id:
            try:
                user_data = self.redis_client.get_user_data(self.client_id)
            except Exception as e:
                self.logger.bind(tag=TAG).warning(f"Failed to get user data for apply_role_config: {e}")
                user_data = None
        
        self.loop.create_task(self.user_profile_manager.apply_role_config(user_data))

    def _save_user_settings(self):
        """保存用户设置到Redis"""
        self.user_profile_manager.save_user_settings()

    async def _save_and_close(self, ws):
        """保存记忆并关闭连接"""
        # 记录连接关闭时间
        close_start_time = time.time()
        
        # 先关闭连接，避免阻塞用户体验
        self.logger.bind(tag=TAG).debug(f"🔌 开始关闭连接: session_id={self.session_id}")
        await self.close(ws)
        
        close_duration = time.time() - close_start_time
        self.logger.bind(tag=TAG).debug(f"🔌 连接关闭完成: 耗时={close_duration:.3f}秒")
        
        # 然后异步保存记忆，不影响连接关闭
        if self.memory:
            try:
                start_time = time.time()
                self.logger.bind(tag=TAG).info(f"💾 连接关闭后开始保存记忆: session_id={self.session_id}, client_id={self.client_id}")
                self.logger.bind(tag=TAG).info(f"💾 对话长度: {len(self.dialogue.dialogue)}条消息")

                # 异步保存对话记忆，不阻塞连接关闭
                await self.memory.save_memory(self.dialogue.dialogue)
                
                save_duration = time.time() - start_time
                self.logger.bind(tag=TAG).info(f"💾 记忆保存完成: 耗时={save_duration:.2f}秒")
                
                # 如果保存时间过长，记录警告（但不影响用户体验）
                if save_duration > 5.0:
                    self.logger.bind(tag=TAG).warning(f"💾 记忆保存耗时过长: {save_duration:.2f}秒 > 5秒（已不影响连接）")
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"💾 保存记忆失败: {e}")
                import traceback
                self.logger.bind(tag=TAG).error(f"💾 保存记忆错误堆栈: {traceback.format_exc()}")
        else:
            self.logger.bind(tag=TAG).debug("💾 无记忆模块，跳过保存")

    # 移除超时相关方法，不再主动断开连接

    async def _route_message(self, message):
        """消息路由"""

        if isinstance(message, str):
            await handleTextMessage(self, message)
        elif isinstance(message, bytes):
            await handleAudioMessage(self, message)

    async def handle_restart(self, message):
        """处理服务器重启请求"""
        try:

            self.logger.bind(tag=TAG).info("收到服务器重启指令，准备执行...")

            # 发送确认响应
            await self.websocket.send(
                json.dumps(
                    {
                        "type": "server_response",
                        "status": "success",
                        "message": "服务器重启中...",
                    }
                )
            )

            # 异步执行重启操作
            def restart_server():
                """实际执行重启的方法"""
                time.sleep(1)
                self.logger.bind(tag=TAG).info("执行服务器重启...")
                subprocess.Popen(
                    [sys.executable, "app.py"],
                    stdin=sys.stdin,
                    stdout=sys.stdout,
                    stderr=sys.stderr,
                    start_new_session=True,
                )
                os._exit(0)

            # 使用线程执行重启避免阻塞事件循环
            threading.Thread(target=restart_server, daemon=True).start()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"重启失败: {str(e)}")
            await self.websocket.send(
                json.dumps(
                    {
                        "type": "server_response",
                        "status": "error",
                        "message": f"Restart failed: {str(e)}",
                    }
                )
            )

    def _initialize_components(self):
        """初始化组件"""
        # 使用角色配置的prompt，而不是配置文件中的prompt
        if self.config.get("prompt") is not None:
            self.prompt = self.config["prompt"]
            self.change_system_prompt(self.prompt)
            self.logger.bind(tag=TAG).info(
                f"初始化组件: 使用角色配置prompt成功 {self.prompt[:50]}..."
            )

        """初始化本地组件"""
        if self.vad is None:
            self.vad = self._vad
        if self.asr is None:
            self.asr = self._asr
        """加载记忆"""
        self._initialize_memory()
        """加载意图识别"""
        self._initialize_intent()
        """初始化上报线程"""
        self._init_report_threads()

    def _init_report_threads(self):
        """初始化ASR和TTS上报线程"""
        if not self.read_config_from_api or self.need_bind:
            return
        if self.chat_history_conf == 0:
            return
        if self.report_thread is None or not self.report_thread.is_alive():
            self.report_thread = threading.Thread(
                target=self._report_worker, daemon=True
            )
            self.report_thread.start()
            self.logger.bind(tag=TAG).info("TTS上报线程已启动")

    def _initialize_private_config(self):
        """如果是从配置文件获取，则进行二次实例化"""
        if not self.read_config_from_api:
            return
        """从接口获取差异化的配置进行二次实例化，非全量重新实例化"""
        try:
            begin_time = time.time()
            private_config = get_private_config_from_api(
                self.config,
                self.headers.get("device-id"),
                self.headers.get("client-id", self.headers.get("device-id")),
            )
            private_config["delete_audio"] = bool(self.config.get("delete_audio", True))
            self.logger.bind(tag=TAG).info(
                f"{time.time() - begin_time} 秒，获取差异化配置成功: {json.dumps(filter_sensitive_info(private_config), ensure_ascii=False)}"
            )
        except DeviceNotFoundException as e:
            self.need_bind = True
            private_config = {}
        except DeviceBindException as e:
            self.need_bind = True
            self.bind_code = e.bind_code
            private_config = {}
        except Exception as e:
            self.need_bind = True
            self.logger.bind(tag=TAG).error(f"获取差异化配置失败: {e}")
            private_config = {}

        init_llm, init_tts, init_memory, init_intent = (
            False,
            False,
            False,
            False,
        )

        init_vad = check_vad_update(self.common_config, private_config)
        init_asr = check_asr_update(self.common_config, private_config)

        if private_config.get("TTS", None) is not None:
            init_tts = True
            self.config["TTS"] = private_config["TTS"]
            self.config["selected_module"]["TTS"] = private_config["selected_module"][
                "TTS"
            ]
            # 确保TTS超时配置也被传递
            if "tts_request_timeout" in self.config:
                private_config["tts_request_timeout"] = self.config["tts_request_timeout"]
        if private_config.get("LLM", None) is not None:
            init_llm = True
            self.config["LLM"] = private_config["LLM"]
            self.config["selected_module"]["LLM"] = private_config["selected_module"][
                "LLM"
            ]
        if private_config.get("Memory", None) is not None:
            init_memory = True
            self.config["Memory"] = private_config["Memory"]
            self.config["selected_module"]["Memory"] = private_config[
                "selected_module"
            ]["Memory"]
        if private_config.get("Intent", None) is not None:
            init_intent = True
            self.config["Intent"] = private_config["Intent"]
            self.config["selected_module"]["Intent"] = private_config[
                "selected_module"
            ]["Intent"]
        if private_config.get("prompt", None) is not None:
            self.config["prompt"] = private_config["prompt"]
        if private_config.get("device_max_output_size", None) is not None:
            self.max_output_size = int(private_config["device_max_output_size"])
        if private_config.get("chat_history_conf", None) is not None:
            self.chat_history_conf = int(private_config["chat_history_conf"])
        try:
            modules = initialize_modules(
                self.logger,
                private_config,
                init_vad,
                init_asr,
                init_llm,
                init_tts,
                init_memory,
                init_intent,
            )
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"初始化组件失败: {e}")
            modules = {}
        if modules.get("tts", None) is not None:
            self.tts = modules["tts"]
        if modules.get("vad", None) is not None:
            self.vad = modules["vad"]
        if modules.get("asr", None) is not None:
            self.asr = modules["asr"]
        if modules.get("llm", None) is not None:
            self.llm = modules["llm"]
        if modules.get("intent", None) is not None:
            self.intent = modules["intent"]
        if modules.get("memory", None) is not None:
            self.memory = modules["memory"]

    def _initialize_memory(self):
        """初始化记忆模块"""
        # 使用 client_id 而不是 device_id 作为记忆存储的键
        self.memory.init_memory(self.client_id, self.llm)

    def _initialize_intent(self):
        self.intent_type = self.config["Intent"][
            self.config["selected_module"]["Intent"]
        ]["type"]
        if self.intent_type == "function_call" or self.intent_type == "intent_llm":
            self.load_function_plugin = True
        """初始化意图识别模块"""
        # 获取意图识别配置
        intent_config = self.config["Intent"]
        intent_type = self.config["Intent"][self.config["selected_module"]["Intent"]][
            "type"
        ]

        # 如果使用 nointent，直接返回
        if intent_type == "nointent":
            return
        # 使用 intent_llm 模式
        elif intent_type == "intent_llm":
            intent_llm_name = intent_config[self.config["selected_module"]["Intent"]][
                "llm"
            ]

            if intent_llm_name and intent_llm_name in self.config["LLM"]:
                # 如果配置了专用LLM，则创建独立的LLM实例
                from core.utils import llm as llm_utils

                intent_llm_config = self.config["LLM"][intent_llm_name]
                intent_llm_type = intent_llm_config.get("type", intent_llm_name)
                intent_llm = llm_utils.create_instance(
                    intent_llm_type, intent_llm_config
                )
                self.logger.bind(tag=TAG).info(
                    f"为意图识别创建了专用LLM: {intent_llm_name}, 类型: {intent_llm_type}"
                )
                self.intent.set_llm(intent_llm)
            else:
                # 否则使用主LLM
                self.intent.set_llm(self.llm)
                self.logger.bind(tag=TAG).info("使用主LLM作为意图识别模型")

        """加载插件"""
        self.func_handler = FunctionHandler(self)
        self.mcp_manager = MCPManager(self)

        """加载MCP工具"""
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.mcp_manager.initialize_servers(), self.loop
            )
            # 等待MCP初始化完成，但有超时保护
            future.result(timeout=60)  # 60秒超时
        except asyncio.TimeoutError:
            self.logger.bind(tag=TAG).warning("MCP initialization timed out")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"MCP initialization failed: {e}")

    def change_system_prompt(self, prompt):
        self.prompt = prompt
        # 更新系统prompt至上下文
        self.dialogue.update_system_message(self.prompt)

    def chat_with_function_calling(self, query, tool_call=False):
        self.logger.bind(tag=TAG).debug(f"Chat with function calling start: {query}")
        """Chat with function calling for intent detection using streaming"""

        if not tool_call:
            # 开始LLM延迟计时
            self.latency_tracker.start("LLM")
            self.dialogue.put(Message(role="user", content=query))
            
            # Handle user reply for proactive message cooldown
            if hasattr(self, 'user_profile_manager') and self.user_profile_manager:
                self.user_profile_manager.handle_user_reply()

        # Define intent functions
        functions = None
        if hasattr(self, "func_handler"):
            self.logger.bind(tag=TAG).debug(f"开始获取函数描述列表")
            if self.func_handler is not None:
                functions = self.func_handler.get_functions()
                self.logger.bind(tag=TAG).debug(f"目前有 {len(functions)} 个函数")
            else:
                self.logger.bind(tag=TAG).debug("FunctionHandler尚未初始化！")
        response_message = []
        processed_chars = 0  # 跟踪已处理的字符位置

        try:
            # 使用记忆模块进行对话历史压缩
            dialogue_with_smart_limit = None
            if self.memory is not None:
                # Function calling通常需要更多context，使用1.5倍的限制
                fc_max_tokens = int(self.max_context_tokens * 1.5)
                self.logger.bind(tag=TAG).debug(f"[DEBUG] Calling compress_context_with_memory with policy_check={self.policy_check is not None}")
                future = asyncio.run_coroutine_threadsafe(
                    self.dialogue.compress_context_with_memory(self.memory, fc_max_tokens, self.policy_check),
                    self.loop
                )
                dialogue_with_smart_limit = future.result()
                self.logger.bind(tag=TAG).info("已通过记忆模块对历史对话进行智能压缩（Function Calling模式）")
                self.logger.bind(tag=TAG).info(f"Function Calling模式：压缩后的对话包含{len(dialogue_with_smart_limit)}条消息")
            else:
                # 如果没有记忆模块，使用原有的智能限制方法
                dialogue_with_smart_limit = self.dialogue.get_llm_dialogue_with_smart_limit(
                    "", self.max_context_tokens, self.max_total_tokens
                )
            self.logger.bind(tag=TAG).info(f"对话记录: {dialogue_with_smart_limit}")
            self.logger.bind(tag=TAG).debug(f"当前查询: {query}")
            self.logger.bind(tag=TAG).debug(f"可用函数数量: {len(functions) if functions else 0}")
            self.logger.bind(tag=TAG).debug(f"智能分配后的对话长度: {len(dialogue_with_smart_limit)}")

            # 记录发送到LLM的完整请求信息
            self.logger.bind(tag=TAG).debug(f"🔍 发送到LLM的请求详情:")
            self.logger.bind(tag=TAG).debug(f"🔍 - session_id: {self.session_id}")
            self.logger.bind(tag=TAG).debug(f"🔍 - dialogue长度: {len(dialogue_with_smart_limit)}")
            self.logger.bind(tag=TAG).debug(f"🔍 - functions数量: {len(functions) if functions else 0}")
            self.logger.bind(tag=TAG).debug(f"🔍 - 对话内容: {dialogue_with_smart_limit}")
            if functions:
                self.logger.bind(tag=TAG).debug(f"🔍 - 函数列表: {[f.get('function', {}).get('name', 'unknown') if isinstance(f, dict) else str(f) for f in functions]}")

            # 检查连接状态，如果已关闭则不启动LLM请求
            if self.client_abort or getattr(self, 'is_connection_closed', False):
                self.logger.bind(tag=TAG).warning(f"连接已关闭，取消LLM请求: {query}")
                return None
            
            # 重置内容拦截标志，开始新的回复
            self.content_blocked = False
            self.processing_redirect_response = False
            
            # 使用支持functions的streaming接口
            llm_responses = self.llm.response_with_functions(
                self.session_id,
                dialogue_with_smart_limit,
                functions=functions,
            )
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"🚨 LLM 处理出错 查询: '{query}'")
            self.logger.bind(tag=TAG).error(f"🚨 错误类型: {type(e).__name__}")
            self.logger.bind(tag=TAG).error(f"🚨 错误信息: {str(e)}")
            self.logger.bind(tag=TAG).error(f"🚨 session_id: {self.session_id}")
            
            # 记录更详细的上下文信息
            try:
                self.logger.bind(tag=TAG).error(f"🚨 对话历史长度: {len(self.dialogue.dialogue) if hasattr(self, 'dialogue') and self.dialogue else 'N/A'}")
                self.logger.bind(tag=TAG).error(f"🚨 memory状态: {self.memory is not None if hasattr(self, 'memory') else 'N/A'}")
                self.logger.bind(tag=TAG).error(f"🚨 func_handler状态: {hasattr(self, 'func_handler') and self.func_handler is not None}")
                if hasattr(self, 'llm') and self.llm:
                    self.logger.bind(tag=TAG).error(f"🚨 LLM类型: {type(self.llm).__name__}")
                    if hasattr(self.llm, 'model_name'):
                        self.logger.bind(tag=TAG).error(f"🚨 LLM模型: {self.llm.model_name}")
            except Exception as ctx_e:
                self.logger.bind(tag=TAG).error(f"🚨 获取上下文信息时出错: {ctx_e}")
            
            # 记录完整的异常堆栈
            import traceback
            self.logger.bind(tag=TAG).error(f"🚨 异常堆栈:\n{traceback.format_exc()}")
            
            return None

        self.llm_finish_task = False
        text_index = 0

        # 处理流式响应
        tool_call_flag = False
        content_arguments = ""
        
        # Accumulator for potentially multiple, indexed tool calls from the stream
        # Each key is an index, value is {'id': str, 'name': str, 'arguments': str, 'processed_in_loop': bool}
        tool_calls_accumulator = {}
        
        for response in llm_responses:
            # 【重要修复】只有在连接真正关闭时才停止LLM处理，用户中断（abort/说话）不应该停止LLM
            if getattr(self, 'is_connection_closed', False):
                self.logger.bind(tag=TAG).warning(f"连接已关闭，停止处理LLM响应")
                break
                
            # 记录完整的响应对象
            self.logger.bind(tag=TAG).debug(f"LLM 响应: {repr(response)}")

            # 检查响应是否为None，如果是则跳过
            if response is None:
                self.logger.bind(tag=TAG).warning("收到None响应，跳过处理")
                continue

            # 安全解包响应 - 处理不同的返回格式
            content = None
            tools_call = None

            try:
                # 尝试解包为元组 (content, tools_call)
                if isinstance(response, (tuple, list)) and len(response) == 2:
                    content, tools_call = response
                    self.logger.bind(tag=TAG).debug(f"解包元组 - content: {repr(content)}, tools_call: {repr(tools_call)}")
                # 如果是字典格式
                elif isinstance(response, dict):
                    content = response.get("content")
                    tools_call = response.get("tools_call")
                    self.logger.bind(tag=TAG).debug(f"解包字典 - content: {repr(content)}, tools_call: {repr(tools_call)}")
                # 如果是单个字符串（不支持函数调用的LLM提供者）
                elif isinstance(response, str):
                    content = response
                    tools_call = None
                    self.logger.bind(tag=TAG).debug(f"单个字符串响应 - content: {repr(content)}")
                else:
                    self.logger.bind(tag=TAG).warning(f"未知的响应格式: {type(response)}, 内容: {repr(response)}")
                    continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"响应解包失败: {e}, 响应内容: {repr(response)}")
                continue
            # 安全检查content是否为None或空
            try:
                self.logger.bind(tag=TAG).debug(f"💬 检查content: type={type(content)}, is_none={content is None}")
                if content is not None:
                    try:
                        content_str = str(content)
                        content_len = len(content_str)
                        self.logger.bind(tag=TAG).debug(f"💬 content转换为字符串成功，长度: {content_len}")
                        if content_len > 0:
                            content_arguments += content_str
                            self.logger.bind(tag=TAG).debug(f"💬 content已添加到content_arguments，当前总长度: {len(content_arguments)}")
                        else:
                            self.logger.bind(tag=TAG).debug(f"💬 content为空字符串，跳过")
                    except Exception as content_error:
                        self.logger.bind(tag=TAG).error(f"💬 处理content时出错: {content_error}")
                        self.logger.bind(tag=TAG).error(f"💬 content的repr: {repr(content)}")
                else:
                    self.logger.bind(tag=TAG).debug(f"💬 content为None，跳过")
            except Exception as check_error:
                self.logger.bind(tag=TAG).error(f"💬 检查content时出错: {check_error}")
                self.logger.bind(tag=TAG).error(f"💬 content的repr: {repr(content)}")
                continue

            if not tool_call_flag and content_arguments.startswith("<tool_call>"):
                # print("content_arguments", content_arguments)
                tool_call_flag = True
                is_collecting_tool_call = True

            # Process tool call deltas using the accumulator
            if tools_call:  # tools_call is a list of tool call deltas
                tool_call_flag = True # Indicate that a tool call is being processed or has been processed
                self.logger.bind(tag=TAG).debug(f"LLM chunk contains tool_call deltas: {repr(tools_call)}")
                for tool_delta in tools_call:
                    current_delta_index = getattr(tool_delta, 'index', 0)
                    
                    # Get or initialize the accumulator for this index
                    call_data = tool_calls_accumulator.setdefault(current_delta_index, {
                        'id': None, 'name': None, 'arguments': "", 'processed_in_loop': False
                    })

                    if call_data['processed_in_loop']:
                        self.logger.bind(tag=TAG).debug(f"Tool call for index {current_delta_index} already processed in loop, skipping delta.")
                        continue # Already processed this one, skip further deltas for it

                    delta_id = getattr(tool_delta, 'id', None)
                    if delta_id:
                        if call_data['id'] and call_data['id'] != delta_id:
                             self.logger.bind(tag=TAG).warning(f"Tool call ID changed mid-accumulation for index {current_delta_index}: {call_data['id']} -> {delta_id}. Using new ID.")
                        call_data['id'] = delta_id
                        self.logger.bind(tag=TAG).debug(f"Accumulated tool_delta.id for index {current_delta_index}: {call_data['id']}")

                    delta_func_name = getattr(tool_delta.function, 'name', None) if hasattr(tool_delta, 'function') else None
                    if delta_func_name:
                        if call_data['name'] and call_data['name'] != delta_func_name:
                            self.logger.bind(tag=TAG).warning(f"Tool call Name changed mid-accumulation for index {current_delta_index}: {call_data['name']} -> {delta_func_name}. Using new Name.")
                        call_data['name'] = delta_func_name
                        self.logger.bind(tag=TAG).debug(f"Accumulated tool_delta.function.name for index {current_delta_index}: {call_data['name']}")
                    
                    delta_func_args = getattr(tool_delta.function, 'arguments', None) if hasattr(tool_delta, 'function') else None
                    if delta_func_args:
                        call_data['arguments'] += delta_func_args
                        self.logger.bind(tag=TAG).debug(f"Accumulated tool_delta.function.arguments for index {current_delta_index}: '{delta_func_args}'. Total args for index {current_delta_index}: '{call_data['arguments']}'")

                # Attempt to process any fully formed tool calls from the accumulator after processing all deltas in this chunk
                for idx, accumulated_call in list(tool_calls_accumulator.items()): # Iterate over a copy for safe modification
                    if accumulated_call['processed_in_loop']:
                        continue # Already handled

                    if accumulated_call['id'] and accumulated_call['name']:
                        current_args_str = accumulated_call['arguments']
                        
                        if current_args_str: # Only try to process if arguments string is not empty
                            try:
                                # This load is just to check validity and completeness.
                                json.loads(current_args_str) 
                                
                                # If json.loads succeeded, arguments are complete and valid.
                                self.logger.bind(tag=TAG).info(f"Processing COMPLETED tool call (from accumulator in-stream, valid JSON) for index {idx}: ID={accumulated_call['id']}, Name={accumulated_call['name']}")
                                self.logger.bind(tag=TAG).debug(f"Arguments for index {idx}: {current_args_str}")
                                try:
                                    self._process_complete_tool_call(accumulated_call['id'], accumulated_call['name'], current_args_str)
                                    accumulated_call['processed_in_loop'] = True # Mark as processed
                                except Exception as e:
                                    self.logger.bind(tag=TAG).error(f"Error processing tool call for index {idx} (Name: {accumulated_call['name']}) from accumulator: {e}")
                                    # Mark as processed even on error to avoid retrying a failing call
                                    accumulated_call['processed_in_loop'] = True 
                            except json.JSONDecodeError:
                                # Arguments are present but not yet valid/complete JSON. Continue accumulating.
                                self.logger.bind(tag=TAG).debug(f"Tool call arguments for index {idx} (Name: {accumulated_call['name']}) not yet valid JSON. Current args: '{current_args_str}'. Will continue accumulating.")
                        # else: current_args_str is empty.
                        #   For functions expecting args, this means args haven't arrived yet.
                        #   For functions like get_time, this is fine, they will be handled by post-loop processing
                        #   if arguments remain empty till the end of the stream.

            # 安全检查content是否为None或空
            try:
                self.logger.bind(tag=TAG).debug(f"📝 检查content用于TTS: type={type(content)}, is_none={content is None}")
                if content is not None:
                    try:
                        content_str = str(content)
                        content_len = len(content_str)
                        self.logger.bind(tag=TAG).debug(f"📝 content转换为字符串成功，长度: {content_len}")
                        if content_len > 0:
                            if not tool_call_flag:
                                self.logger.bind(tag=TAG).debug(f"📝 非工具调用模式，处理文本内容")
                        else:
                            self.logger.bind(tag=TAG).debug(f"📝 content为空字符串，跳过TTS处理")
                            continue
                    except Exception as content_error:
                        self.logger.bind(tag=TAG).error(f"📝 处理content时出错: {content_error}")
                        self.logger.bind(tag=TAG).error(f"📝 content的repr: {repr(content)}")
                        continue
                else:
                    self.logger.bind(tag=TAG).debug(f"📝 content为None，跳过TTS处理")
                    continue
            except Exception as check_error:
                self.logger.bind(tag=TAG).error(f"📝 检查content时出错: {check_error}")
                self.logger.bind(tag=TAG).error(f"📝 content的repr: {repr(content)}")
                continue
            
            if content is not None and len(str(content)) > 0:
                if not tool_call_flag:
                    response_message.append(str(content))
                    self.logger.bind(tag=TAG).debug(f"📝 LLM输出累积: content='{content}', 累积长度={len(response_message)}")
                    self.logger.bind(tag=TAG).debug(f"📝 当前response_message: {response_message}")

                    if self.client_abort:
                        break

                    # end_time = time.time()
                    # self.logger.bind(tag=TAG).debug(f"大模型返回时间: {end_time - start_time:.2f} 秒, 生成token={content}")

                    # 处理文本分段和TTS逻辑
                    # 合并当前全部文本并处理未分割部分
                    full_text = "".join(response_message)
                    
                    # 对完整累积文本进行内容安全检测
                    if not getattr(self, 'content_blocked', False):
                        try:
                            # 获取最近的用户问题（用于answer检测）
                            user_question = ""
                            if hasattr(self, 'dialogue') and self.dialogue:
                                # 从对话历史中获取最后一个用户消息
                                for message in reversed(self.dialogue.dialogue):
                                    if message.role == "user":
                                        user_question = message.content
                                        break
                            
                            # 在事件循环中运行异步检测
                            loop = None
                            try:
                                loop = asyncio.get_event_loop()
                            except RuntimeError:
                                # 如果没有事件循环，创建新的
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                            
                            # 执行内容安全检测 - 对完整累积文本进行检测
                            check_result = loop.run_until_complete(
                                self.policy_check.check_answer(user_question, full_text)
                            )
                            
                            # 如果内容被拦截，停止后续处理并开始引导性回复流
                            if check_result.get("is_blocked", False):
                                # 设置拦截标志，后续所有内容都不再处理
                                self.content_blocked = True
                                self.logger.bind(tag=TAG).warning(f"🚫 内容被拦截，切换到引导性回复流: {full_text[:100]}...")
                                
                                # 清空现有的TTS队列
                                self._clear_tts_queue()
                                
                                # 开始流式生成引导性回复
                                try:
                                    self.logger.bind(tag=TAG).info("🔄 开始流式生成引导性回复")
                                    
                                    # 设置引导性回复处理标记
                                    self.processing_redirect_response = True
                                    
                                    # 初始化引导性回复的累积变量
                                    redirect_response_message = []
                                    redirect_processed_chars = 0
                                    redirect_text_index = text_index
                                    
                                    # 使用流式生成引导性回复
                                    redirect_stream = self.policy_check.generate_redirect_response_stream(
                                        self.llm, self.session_id, full_text
                                    )
                                    
                                    # 处理引导性回复的流式输出
                                    for redirect_content in redirect_stream:
                                        if self.client_abort or getattr(self, 'is_connection_closed', False):
                                            self.logger.bind(tag=TAG).info("客户端中断，停止引导性回复生成")
                                            break
                                            
                                        if redirect_content and len(str(redirect_content)) > 0:
                                            redirect_response_message.append(str(redirect_content))
                                            self.logger.bind(tag=TAG).debug(f"🔄 引导性回复累积: '{redirect_content}'")
                                            
                                            # 处理引导性回复的文本分段和TTS逻辑
                                            redirect_full_text = "".join(redirect_response_message)
                                            redirect_current_text = redirect_full_text[redirect_processed_chars:]
                                            
                                            # 查找文本分割点
                                            redirect_last_punct_pos = find_text_split_point(redirect_current_text, getattr(self, 'min_segment_length', 4))
                                            
                                            # 找到分割点则处理
                                            if redirect_last_punct_pos != -1:
                                                redirect_segment_text_raw = redirect_current_text[:redirect_last_punct_pos + 1]
                                                redirect_segment_text = get_string_no_punctuation_or_emoji(redirect_segment_text_raw)
                                                
                                                if redirect_segment_text:
                                                    redirect_text_index += 1
                                                    self.recode_first_last_text(redirect_segment_text_raw, redirect_text_index)
                                                    future = self.executor.submit(
                                                        self.speak_and_play, redirect_segment_text_raw, redirect_text_index, True
                                                    )
                                                    self.tts_queue.put((future, redirect_text_index))
                                                    redirect_processed_chars += len(redirect_segment_text_raw)
                                                    
                                                    self.logger.bind(tag=TAG).debug(f"🔄 引导性回复分段发送TTS: '{redirect_segment_text_raw}'")
                                    
                                    # 处理引导性回复的剩余文本
                                    if redirect_response_message:
                                        redirect_full_text = "".join(redirect_response_message)
                                        redirect_remaining_text = redirect_full_text[redirect_processed_chars:].strip()
                                        
                                        if redirect_remaining_text:
                                            self.logger.bind(tag=TAG).debug(f"🔄 处理引导性回复剩余文本: '{redirect_remaining_text}'")
                                            
                                            # 对剩余文本进行智能分段处理
                                            segments = segment_text_with_merge(redirect_remaining_text, getattr(self, 'min_segment_length', 4))
                                            
                                            for segment in segments:
                                                cleaned_segment = get_string_no_punctuation_or_emoji(segment)
                                                if cleaned_segment:
                                                    redirect_text_index += 1
                                                    self.recode_first_last_text(segment, redirect_text_index)
                                                    future = self.executor.submit(
                                                        self.speak_and_play, segment, redirect_text_index, True
                                                    )
                                                    self.tts_queue.put((future, redirect_text_index))
                                        
                                        # 保存完整的引导性回复到对话历史中（替代原始回复）
                                        self.dialogue.put(Message(role="assistant", content=redirect_full_text))
                                        self.logger.bind(tag=TAG).info(f"🔄 引导性回复已保存到对话历史: {redirect_full_text}")
                                    
                                    # 清除引导性回复处理标记
                                    self.processing_redirect_response = False
                                    
                                    # 跳出原始LLM流处理循环
                                    break
                                    
                                except Exception as e:
                                    self.logger.bind(tag=TAG).error(f"流式生成引导性回复失败: {e}")
                                    # 使用默认引导语作为fallback
                                    default_redirect = "哎呀，咱们闺蜜之间聊天，不聊这个话题好不好？我们来聊聊其他有趣的事情吧！"
                                    
                                    # 设置引导性回复处理标记（如果还没设置的话）
                                    self.processing_redirect_response = True
                                    
                                    # 保存默认引导语到对话历史中（替代原始回复）
                                    self.dialogue.put(Message(role="assistant", content=default_redirect))
                                    
                                    # 直接处理默认引导语
                                    text_index += 1
                                    self.recode_first_last_text(default_redirect, text_index)
                                    future = self.executor.submit(
                                        self.speak_and_play, default_redirect, text_index, True
                                    )
                                    self.tts_queue.put((future, text_index))
                                    
                                    # 清除引导性回复处理标记
                                    self.processing_redirect_response = False
                                    
                                    break  # 跳出流式处理循环
                                    
                        except Exception as e:
                            self.logger.bind(tag=TAG).error(f"内容安全检测异常: {e}")
                            # 检测失败不影响正常流程，继续处理
                    
                    current_text = full_text[processed_chars:]  # 从未处理的位置开始

                    # 查找文本分割点
                    last_punct_pos = find_text_split_point(current_text, getattr(self, 'min_segment_length', 4))

                    # 找到分割点则处理
                    if last_punct_pos != -1:
                        segment_text_raw = current_text[: last_punct_pos + 1]
                        segment_text = get_string_no_punctuation_or_emoji(
                            segment_text_raw
                        )
                        
                        # 详细日志追踪分段逻辑
                        self.logger.bind(tag=TAG).debug(f"🔍 文本分段详情:")
                        self.logger.bind(tag=TAG).debug(f"  current_text: '{current_text}'")
                        self.logger.bind(tag=TAG).debug(f"  last_punct_pos: {last_punct_pos}")
                        self.logger.bind(tag=TAG).debug(f"  segment_text_raw: '{segment_text_raw}'")
                        self.logger.bind(tag=TAG).debug(f"  segment_text (去标点): '{segment_text}'")
                        self.logger.bind(tag=TAG).debug(f"  processed_chars before: {processed_chars}")
                        self.logger.bind(tag=TAG).debug(f"  processed_chars will add: {len(segment_text_raw)}")
                        
                        if segment_text:  # 用清理后的文本检查是否有有效内容
                            text_index += 1
                            # 保存原始文本用于记录，TTS使用原始文本以保留标点符号
                            self.recode_first_last_text(segment_text_raw, text_index)
                            future = self.executor.submit(
                                self.speak_and_play, segment_text_raw, text_index  # 使用原始文本保留标点符号
                            )
                            self.tts_queue.put((future, text_index))
                            # 更新已处理字符位置
                            processed_chars += len(segment_text_raw)
                            self.logger.bind(tag=TAG).debug(f"  processed_chars after: {processed_chars}")

        # After the loop, process any remaining unprocessed text with segmentation
        if not tool_call_flag and not self.client_abort and response_message:
            full_text = "".join(response_message)
            remaining_text_before_strip = full_text[processed_chars:]
            remaining_text = remaining_text_before_strip.strip()
            
            # 详细日志追踪剩余文本处理
            self.logger.bind(tag=TAG).debug(f"🔍 剩余文本处理详情:")
            self.logger.bind(tag=TAG).debug(f"  full_text: '{full_text}'")
            self.logger.bind(tag=TAG).debug(f"  processed_chars: {processed_chars}")
            self.logger.bind(tag=TAG).debug(f"  remaining_text_before_strip: '{remaining_text_before_strip}'")
            self.logger.bind(tag=TAG).debug(f"  remaining_text_after_strip: '{remaining_text}'")
            
            if remaining_text:
                self.logger.bind(tag=TAG).debug(f"Processing remaining text: '{remaining_text}' (length: {len(remaining_text)})")
                
                # 对剩余文本进行智能分段处理（合并短句）
                segments = segment_text_with_merge(remaining_text, getattr(self, 'min_segment_length', 4))
                
                for segment in segments:
                    cleaned_segment = get_string_no_punctuation_or_emoji(segment)
                    
                    if cleaned_segment:  # 用清理后的文本检查是否有有效内容
                        text_index += 1
                        # 保存原始segment用于记录，但TTS使用原始segment以保留标点符号
                        self.recode_first_last_text(segment, text_index)
                        future = self.executor.submit(
                            self.speak_and_play, segment, text_index  # 使用原始segment保留标点符号
                        )
                        self.tts_queue.put((future, text_index))
                        self.logger.bind(tag=TAG).debug(f"Segmented remaining text: '{segment}' -> TTS")
            else:
                self.logger.bind(tag=TAG).debug("No remaining text to process after main loop")

        # After the loop, process any remaining accumulated tool calls that weren't processed in-stream
        for idx, call_parts in list(tool_calls_accumulator.items()): # Iterate over a copy
            if call_parts['processed_in_loop']:
                continue # Already handled

            if call_parts['id'] and call_parts['name']:
                self.logger.bind(tag=TAG).info(f"Processing REMAINING accumulated tool call (post-stream) for index {idx}: ID={call_parts['id']}, Name={call_parts['name']}")
                
                final_args_str = call_parts['arguments']
                if not final_args_str: # If arguments are empty string after all deltas
                    final_args_str = "{}"
                    self.logger.bind(tag=TAG).debug(f"Arguments for remaining call (index {idx}, Name: {call_parts['name']}) were empty, defaulted to '{{}}'.")
                else:
                    self.logger.bind(tag=TAG).debug(f"Arguments for remaining call (index {idx}, Name: {call_parts['name']}): {final_args_str}")

                try:
                    json.loads(final_args_str) # Validate JSON one last time
                    self._process_complete_tool_call(call_parts['id'], call_parts['name'], final_args_str)
                except json.JSONDecodeError:
                    self.logger.bind(tag=TAG).error(f"Remaining accumulated tool call arguments for index {idx} (Name: {call_parts['name']}) are not valid JSON: '{final_args_str}'. Discarding.")
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"Error processing remaining accumulated tool call for index {idx} (Name: {call_parts['name']}): {e}")
            else:
                self.logger.bind(tag=TAG).warning(f"Skipping incomplete remaining tool call in accumulator for index {idx}: {call_parts}")
        tool_calls_accumulator.clear()
        # If there was no tool call, and we have a text response, save it to dialogue history.
        if not tool_call_flag and not self.client_abort and response_message:
            final_response_text = "".join(response_message)
            
            # 如果内容被拦截，引导性回复已经在流式处理中保存到对话历史了
            if getattr(self, 'content_blocked', False):
                self.logger.bind(tag=TAG).info("内容已被拦截，引导性回复已在流式处理中保存到对话历史")
            else:
                self.dialogue.put(Message(role="assistant", content=final_response_text))
                self.logger.bind(tag=TAG).debug(f"Saved assistant text response to dialogue: {final_response_text}")
        elif tool_call_flag:
            self.logger.bind(tag=TAG).debug("Tool call was processed, not saving separate assistant text message.")
        elif self.client_abort:
            self.logger.bind(tag=TAG).debug("Client aborted, not saving assistant text message.")
        elif not response_message:
            self.logger.bind(tag=TAG).debug("No assistant text message to save.")

        self.llm_finish_task = True
        self.logger.bind(tag=TAG).debug(
            json.dumps(self.dialogue.get_llm_dialogue(), indent=4, ensure_ascii=False)
        )

        return True

    def _process_complete_tool_call(self, function_id, function_name, function_arguments):
        """
        处理完整的工具调用
        
        Args:
            function_id: 工具调用ID
            function_name: 工具调用函数名
            function_arguments: 工具调用参数（JSON字符串）
        """
        if not function_name or not function_arguments:
            self.logger.bind(tag=TAG).warning("工具调用信息不完整，无法处理")
            return
            
        try:
            # 尝试解析JSON参数
            try:
                args_dict = json.loads(function_arguments)
                self.logger.bind(tag=TAG).info(f"工具调用参数解析成功: {args_dict}")
            except json.JSONDecodeError as e:
                # 如果JSON解析失败，尝试修复
                self.logger.bind(tag=TAG).warning(f"工具调用参数解析失败，尝试修复: {e}")
                # 创建一个模拟的工具调用对象用于修复
                class MockFunction:
                    def __init__(self, name, arguments):
                        self.name = name
                        self.arguments = arguments
                        
                class MockToolCall:
                    def __init__(self, id, function):
                        self.id = id
                        self.function = function
                
                mock_function = MockFunction(function_name, function_arguments)
                mock_tool_call = MockToolCall(function_id, mock_function)
                
                # 修复参数
                fixed_arguments = fix_tool_call_arguments(mock_tool_call, self.logger.bind(tag=TAG))
                try:
                    args_dict = json.loads(fixed_arguments)
                    self.logger.bind(tag=TAG).info(f"工具调用参数修复成功: {args_dict}")
                except json.JSONDecodeError as e:
                    self.logger.bind(tag=TAG).error(f"工具调用参数修复失败: {e}")
                    return
            
            # 处理工具调用
            self.logger.bind(tag=TAG).info(f"处理工具调用: {function_name} 参数: {args_dict}")
            
            # 准备工具调用数据
            function_call_data = {
                "name": function_name,
                "id": function_id if function_id else str(uuid.uuid4().hex),
                "arguments": args_dict if isinstance(args_dict, str) else json.dumps(args_dict, ensure_ascii=False)
            }
            
            # 准备调用结果处理
            text_index = 0  # 默认文本索引
            result = None
            
            # 区分MCP工具和普通函数调用
            if hasattr(self, "mcp_manager") and self.mcp_manager.is_mcp_tool(function_name):
                # 如果是MCP工具，调用MCP处理函数
                self.logger.bind(tag=TAG).debug(f"检测到MCP工具调用: {function_name}")
                result = self._handle_mcp_tool_call(function_call_data)
            elif hasattr(self, "func_handler"):
                # 如果是普通函数调用，调用function handler
                self.logger.bind(tag=TAG).debug(f"检测到普通函数调用: {function_name}")
                result = self.func_handler.handle_llm_function_call(self, function_call_data)
            else:
                self.logger.bind(tag=TAG).warning(f"无法处理工具调用: {function_name}，未找到处理程序")
                from plugins_func.register import Action, ActionResponse
                result = ActionResponse(Action.ERROR, f"找不到处理函数: {function_name}")
            
            # 处理函数调用结果
            if result is not None:
                try:
                    self._handle_function_result(result, function_call_data, text_index)
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"处理函数调用结果出错: {e}")
                    traceback.print_exc()
            
            return result
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"处理工具调用出错: {e}")
            traceback.print_exc()
    
    def _handle_mcp_tool_call(self, function_call_data):
        function_arguments = function_call_data["arguments"]
        function_name = function_call_data["name"]
        try:
            args_dict = function_arguments
            if isinstance(function_arguments, str):
                try:
                    args_dict = json.loads(function_arguments)
                except json.JSONDecodeError:
                    self.logger.bind(tag=TAG).error(
                        f"无法解析 function_arguments: {function_arguments}"
                    )
                    return ActionResponse(
                        action=Action.REQLLM, result="参数解析失败", response=""
                    )

            # 添加超时处理，避免无限等待
            try:
                self.logger.bind(tag=TAG).debug(f"开始执行MCP工具: {function_name}, 参数: {args_dict}")
                tool_result = asyncio.run_coroutine_threadsafe(
                    self.mcp_manager.execute_tool(function_name, args_dict), self.loop
                ).result(timeout=30)  # 30秒超时
                self.logger.bind(tag=TAG).debug(f"MCP工具执行完成: {function_name}, 结果类型: {type(tool_result)}")
                self.logger.bind(tag=TAG).debug(f"MCP工具执行结果: {repr(tool_result)}")
            except asyncio.TimeoutError:
                self.logger.bind(tag=TAG).error(f"MCP工具调用超时: {function_name}")
                return ActionResponse(
                    action=Action.REQLLM, result="搜索服务响应超时，请稍后再试", response=""
                )
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"MCP工具调用异常: {function_name}, 错误: {e}")
                import traceback
                self.logger.bind(tag=TAG).error(f"MCP工具调用异常堆栈: {traceback.format_exc()}")
                return ActionResponse(
                    action=Action.REQLLM, result="搜索服务暂时不可用，请稍后再试", response=""
                )
            # meta=None content=[TextContent(type='text', text='北京当前天气:\n温度: 21°C\n天气: 晴\n湿度: 6%\n风向: 西北 风\n风力等级: 5级', annotations=None)] isError=False
            content_text = ""
            self.logger.bind(tag=TAG).debug(f"检查MCP工具结果: tool_result is None: {tool_result is None}")
            if tool_result is not None:
                self.logger.bind(tag=TAG).debug(f"MCP工具结果有内容: tool_result.content is None: {tool_result.content is None}")
                if tool_result.content is not None:
                    self.logger.bind(tag=TAG).debug(f"MCP工具内容数量: {len(tool_result.content)}")
                    for i, content in enumerate(tool_result.content):
                        content_type = content.type
                        self.logger.bind(tag=TAG).debug(f"内容 {i}: 类型={content_type}")
                        if content_type == "text":
                            # 安全处理content.text，确保不为None
                            text_content = content.text if content.text is not None else ""
                            self.logger.bind(tag=TAG).debug(f"内容 {i}: 文本长度={len(text_content)}")
                            self.logger.bind(tag=TAG).debug(f"内容 {i}: 文本预览={text_content[:200]}...")
                            content_text += text_content
                        elif content_type == "image":
                            self.logger.bind(tag=TAG).debug(f"内容 {i}: 图片类型，跳过")
                else:
                    self.logger.bind(tag=TAG).warning("MCP工具返回结果但content为None")
            else:
                self.logger.bind(tag=TAG).warning("MCP工具返回None结果")

            # 安全检查content_text是否为None或空
            self.logger.bind(tag=TAG).debug(f"🔧 检查content_text: type={type(content_text)}, is_none={content_text is None}")
            if content_text is not None:
                try:
                    content_text_len = len(content_text)
                    self.logger.bind(tag=TAG).debug(f"🔧 最终内容文本长度: {content_text_len}")
                    self.logger.bind(tag=TAG).debug(f"🔧 最终内容文本前200字符: {str(content_text)[:200]}...")
                except Exception as len_error:
                    self.logger.bind(tag=TAG).error(f"🔧 计算content_text长度时出错: {len_error}")
                    self.logger.bind(tag=TAG).error(f"🔧 content_text类型: {type(content_text)}")
                    self.logger.bind(tag=TAG).error(f"🔧 content_text的repr: {repr(content_text)}")
                    return ActionResponse(
                        action=Action.REQLLM, result="内容长度计算失败", response=""
                    )
            else:
                self.logger.bind(tag=TAG).warning("🔧 content_text为None")
                return ActionResponse(
                    action=Action.REQLLM, result="搜索没有找到相关结果", response=""
                )

            if content_text is not None and len(str(content_text)) > 0:
                # 清理MCP工具返回的内容，避免过长
                self.logger.bind(tag=TAG).debug(f"🔧 开始清理MCP内容，原始长度: {len(content_text)}")
                cleaned_content = self._clean_mcp_content(content_text, function_name)
                self.logger.bind(tag=TAG).debug(f"🔧 清理后内容长度: {len(cleaned_content)}")
                self.logger.bind(tag=TAG).debug(f"🔧 清理后内容预览: {cleaned_content[:200]}...")
                return ActionResponse(
                    action=Action.REQLLM, result=cleaned_content, response=""
                )
            else:
                self.logger.bind(tag=TAG).warning("🔧 MCP工具返回空内容或None")
                return ActionResponse(
                    action=Action.REQLLM, result="搜索没有找到相关结果", response=""
                )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"MCP工具调用错误: {e}")
            return ActionResponse(
                action=Action.REQLLM, result="工具调用出错", response=""
            )

        return ActionResponse(action=Action.REQLLM, result="工具调用出错", response="")

    def _clean_mcp_content(self, content_text, function_name):
        """清理MCP工具返回的内容，避免过长"""
        try:
            self.logger.bind(tag=TAG).debug(f"🧹 _clean_mcp_content开始: function_name={function_name}")
            self.logger.bind(tag=TAG).debug(f"🧹 content_text类型: {type(content_text)}")
            self.logger.bind(tag=TAG).debug(f"🧹 content_text是否为None: {content_text is None}")
            
            if content_text is not None:
                try:
                    content_length = len(content_text)
                    self.logger.bind(tag=TAG).debug(f"🧹 content_text长度: {content_length}")
                    self.logger.bind(tag=TAG).debug(f"🧹 content_text前200字符: {str(content_text)[:200]}...")
                except Exception as len_error:
                    self.logger.bind(tag=TAG).error(f"🧹 计算content_text长度时出错: {len_error}")
                    self.logger.bind(tag=TAG).error(f"🧹 content_text的repr: {repr(content_text)}")
                    # 尝试转换为字符串
                    try:
                        content_text = str(content_text)
                        self.logger.bind(tag=TAG).debug(f"🧹 转换为字符串后长度: {len(content_text)}")
                    except Exception as str_error:
                        self.logger.bind(tag=TAG).error(f"🧹 转换为字符串失败: {str_error}")
                        return "内容类型转换失败"
            else:
                self.logger.bind(tag=TAG).warning(f"🧹 content_text为None，返回默认消息")
                return "MCP工具返回空内容"

            from core.mcp.content_cleaners import MCPCleanerFactory

            self.logger.bind(tag=TAG).debug(f"🧹 开始调用MCPCleanerFactory.clean_content")
            # 使用清理器工厂创建对应的清理器并清理内容
            result = MCPCleanerFactory.clean_content(function_name, content_text)

            self.logger.bind(tag=TAG).debug(f"🧹 MCPCleanerFactory.clean_content完成")
            self.logger.bind(tag=TAG).debug(f"🧹 清理结果类型: {type(result)}")
            self.logger.bind(tag=TAG).debug(f"🧹 清理结果是否为None: {result is None}")
            
            if result is not None:
                try:
                    result_length = len(result)
                    self.logger.bind(tag=TAG).debug(f"🧹 清理结果长度: {result_length}")
                    self.logger.bind(tag=TAG).debug(f"🧹 清理结果前200字符: {str(result)[:200]}...")
                except Exception as result_len_error:
                    self.logger.bind(tag=TAG).error(f"🧹 计算清理结果长度时出错: {result_len_error}")
                    self.logger.bind(tag=TAG).error(f"🧹 清理结果的repr: {repr(result)}")
                    # 尝试转换为字符串
                    try:
                        result = str(result)
                        self.logger.bind(tag=TAG).debug(f"🧹 转换清理结果为字符串后长度: {len(result)}")
                    except Exception as result_str_error:
                        self.logger.bind(tag=TAG).error(f"🧹 转换清理结果为字符串失败: {result_str_error}")
                        return "清理结果类型转换失败"
            else:
                self.logger.bind(tag=TAG).warning(f"🧹 清理结果为None，返回默认消息")
                return "内容清理后为空"

            return result
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"🧹 _clean_mcp_content异常: {e}")
            self.logger.bind(tag=TAG).error(f"🧹 异常类型: {type(e).__name__}")
            import traceback
            self.logger.bind(tag=TAG).error(f"🧹 异常堆栈: {traceback.format_exc()}")
            # 返回原始内容或安全的默认值
            if content_text is not None:
                try:
                    return str(content_text)[:2000]  # 截断到安全长度
                except Exception as fallback_error:
                    self.logger.bind(tag=TAG).error(f"🧹 fallback处理失败: {fallback_error}")
                    return "内容处理完全失败"
            else:
                return "内容清理失败"



    def _should_save_function_response_to_dialogue(self, function_call_data):
        """判断函数响应是否应该保存到对话历史中"""
        function_name = function_call_data.get("name")
        
        if function_name and hasattr(self, "func_handler"):
            func_item = self.func_handler.get_function(function_name)
            if func_item and hasattr(func_item, 'type'):
                # 系统控制类、IOT控制类的响应不保存到对话历史
                from plugins_func.register import ToolType
                if func_item.type in [ToolType.SYSTEM_CTL, ToolType.IOT_CTL]:
                    return False
        
        return True

    def _handle_function_result(self, result, function_call_data, text_index):
        if result.action == Action.RESPONSE:  # 直接回复前端
            text = result.response
            self.recode_first_last_text(text, text_index)
            future = self.executor.submit(self.speak_and_play, text, text_index)
            self.tts_queue.put((future, text_index))
            
            # 只有非系统控制类的响应才保存到对话历史
            if self._should_save_function_response_to_dialogue(function_call_data):
                self.dialogue.put(Message(role="assistant", content=text))
            else:
                function_name = function_call_data.get("name", "unknown")
                self.logger.bind(tag=TAG).debug(f"系统控制函数 {function_name} 的响应不保存到对话历史: {text}")
        elif result.action == Action.REQLLM:  # 调用函数后再请求llm生成回复
            text = result.result
            # 安全检查text是否为None或空
            if text is not None and len(str(text)) > 0:
                function_id = function_call_data["id"]
                function_name = function_call_data["name"]
                function_arguments = function_call_data["arguments"]
                
                # 确保参数是有效的 JSON
                try:
                    if isinstance(function_arguments, str):
                        json.loads(function_arguments)
                except json.JSONDecodeError:
                    # 如果 JSON 无效，尝试修复
                    self.logger.bind(tag=TAG).warning(f"对话历史中的函数参数 JSON 无效: {function_arguments}")
                    if function_name == "get_weather" and '"location": "lang":' in function_arguments:
                        function_arguments = '{"lang": "zh_CN"}'
                        self.logger.bind(tag=TAG).info(f"修复对话历史中的 {function_name} 参数: {function_arguments}")
                        # 更新 function_call_data 以确保后续使用的是修复后的参数
                        function_call_data["arguments"] = function_arguments
                self.dialogue.put(
                    Message(
                        role="assistant",
                        tool_calls=[
                            {
                                "id": function_id,
                                "function": {
                                    "arguments": function_arguments,
                                    "name": function_name,
                                },
                                "type": "function",
                                "index": 0,
                            }
                        ],
                    )
                )

                self.dialogue.put(
                    Message(
                        role="tool",
                        tool_call_id=(
                            str(uuid.uuid4()) if function_id is None else function_id
                        ),
                        content=text,
                    )
                )
                # 调用LLM生成回复，并检查返回值
                try:
                    llm_result = self.chat_with_function_calling(text, tool_call=True)
                    if llm_result is None:
                        self.logger.bind(tag=TAG).warning("LLM处理工具调用结果后返回None，可能是响应生成失败")
                        # 可以选择提供一个默认回复或者静默处理
                        # 这里选择静默处理，不影响用户体验
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"LLM处理工具调用结果时出错: {e}")
                    # 静默处理错误，不影响用户体验
        elif result.action == Action.NOTFOUND or result.action == Action.ERROR:
            text = result.result
            self.recode_first_last_text(text, text_index)
            future = self.executor.submit(self.speak_and_play, text, text_index)
            self.tts_queue.put((future, text_index))
            
            # 只有非系统控制类的错误响应才保存到对话历史
            if self._should_save_function_response_to_dialogue(function_call_data):
                self.dialogue.put(Message(role="assistant", content=text))
            else:
                function_name = function_call_data.get("name", "unknown")
                self.logger.bind(tag=TAG).debug(f"系统控制函数 {function_name} 的错误响应不保存到对话历史: {text}")
        elif result.action == Action.NONE:
            # 静默处理，不做任何操作，只记录日志
            function_name = function_call_data.get("name", "unknown")
            self.logger.bind(tag=TAG).debug(f"函数 {function_name} 静默执行完成: {result.result}")
        else:
            function_name = function_call_data.get("name", "unknown")
            self.logger.bind(tag=TAG).warning(f"未知的函数响应动作类型: {result.action}, 函数: {function_name}")

    def _tts_priority_thread(self):
        consecutive_timeouts = 0  # 连续超时计数器
        max_consecutive_timeouts = 3  # 最大连续超时次数
        
        while not self.stop_event.is_set():
            text = None
            future = None
            text_index = None
            try:
                try:
                    item = self.tts_queue.get(timeout=1)
                    if item is None:
                        # 【CPU修复】收到毒丸信号，退出线程
                        self.logger.bind(tag=TAG).info(f"🔌 TTS线程收到毒丸信号，正在退出")
                        break
                    future, text_index = item  # 解包获取 Future 和 text_index
                except queue.Empty:
                    if self.stop_event.is_set():
                        break
                    continue
                if future is None:
                    continue
                    
                # 在处理TTS任务前检查是否被用户中断或内容被拦截
                if self.client_abort:
                    self.logger.bind(tag=TAG).debug(f"🔇 TTS任务丢弃 - 客户端已中断: {text_index}")
                    continue
                if getattr(self, 'content_blocked', False):
                    self.logger.bind(tag=TAG).debug(f"🚫 TTS任务丢弃 - 内容已被拦截: {text_index}")
                    continue
                audio_datas, tts_file = [], None
                try:
                    self.logger.bind(tag=TAG).debug("正在处理TTS任务...")
                    tts_timeout = int(self.config.get("tts_timeout", 10))
                    tts_file, text, _ = future.result(timeout=tts_timeout)
                    
                    # 重置连续超时计数器
                    consecutive_timeouts = 0
                    
                    if text is None or len(text) <= 0:
                        self.logger.bind(tag=TAG).error(
                            f"TTS出错：{text_index}: tts text is empty"
                        )
                    elif tts_file is None:
                        self.logger.bind(tag=TAG).error(
                            f"TTS出错： file is empty: {text_index}: {text}"
                        )
                    else:
                        self.logger.bind(tag=TAG).debug(
                            f"TTS生成：文件路径: {tts_file}"
                        )
                        if os.path.exists(tts_file):
                            if self.audio_format == "pcm":
                                audio_datas, _ = self.tts.audio_to_pcm_data(tts_file)
                            else:
                                audio_datas, _ = self.tts.audio_to_opus_data(tts_file)
                            # 在这里上报TTS数据
                            enqueue_tts_report(self, text, audio_datas)
                        else:
                            self.logger.bind(tag=TAG).error(
                                f"TTS出错：文件不存在{tts_file}"
                            )
                except TimeoutError:
                    if self.stop_event.is_set():
                        # Connection closed during TTS processing
                        processing_text = "未知文本"
                        if hasattr(future, '_args') and future._args:
                            try:
                                processing_text = str(future._args[0])[:50] + "..." if len(str(future._args[0])) > 50 else str(future._args[0])
                            except:
                                processing_text = f"text_index={text_index}"
                        elif text_index is not None:
                            processing_text = f"text_index={text_index}"
                        
                        self.logger.bind(tag=TAG).info(
                            f"TTS task for '{processing_text}' (text_index='{text_index}') cancelled due to client disconnect during processing."
                        )
                        # Attempt to cancel the timed-out Future task
                        if future and not future.done():
                            try:
                                future.cancel()
                                self.logger.bind(tag=TAG).debug(f"🎵 Cancelled TTS task for '{processing_text}' (disconnected client).")
                            except Exception as cancel_e:
                                self.logger.bind(tag=TAG).warning(f"🎵 Failed to cancel TTS task for '{processing_text}' (disconnected client): {cancel_e}")
                    else:
                        # Actual TTS Timeout (connection still active)
                        consecutive_timeouts += 1
                        # Use a different variable name for timeout value within this specific scope to avoid conflict
                        tts_timeout_val = int(self.config.get("tts_timeout", 10))
                        
                        processing_text = "未知文本"
                        if hasattr(future, '_args') and future._args:
                            try:
                                processing_text = str(future._args[0])[:50] + "..." if len(str(future._args[0])) > 50 else str(future._args[0])
                            except:
                                processing_text = f"text_index={text_index}"
                        elif text_index is not None:
                            processing_text = f"text_index={text_index}"
                        
                        self.logger.bind(tag=TAG).error(f"🎵 TTS超时: text='{processing_text}', timeout={tts_timeout_val}秒")
                        self.logger.bind(tag=TAG).error(f"🎵 TTS超时统计: 连续超时={consecutive_timeouts}/{max_consecutive_timeouts}")
                        self.logger.bind(tag=TAG).error(f"🎵 会话信息: session_id={self.session_id}, client_id={self.client_id}")
                        
                        if future and not future.done():
                            try:
                                future.cancel()
                                self.logger.bind(tag=TAG).debug("🎵 已取消超时的TTS任务")
                            except Exception as cancel_e:
                                self.logger.bind(tag=TAG).warning(f"🎵 取消TTS任务失败: {cancel_e}")
                        
                        if consecutive_timeouts >= max_consecutive_timeouts:
                            self.logger.bind(tag=TAG).warning(f"🎵 TTS连续超时{consecutive_timeouts}次，清理状态并通知客户端")
                            self.logger.bind(tag=TAG).warning(f"🎵 TTS配置: timeout={tts_timeout_val}秒, request_timeout={self.config.get('tts_request_timeout', 10)}秒")
                            self.clearSpeakStatus()
                            try:
                                asyncio.run_coroutine_threadsafe(
                                    self.websocket.send(
                                        json.dumps(
                                            {
                                                "type": "tts",
                                                "state": "error",
                                                "message": "TTS服务响应超时，请稍后重试",
                                                "session_id": self.session_id,
                                            }
                                        )
                                    ),
                                    self.loop,
                                )
                            except Exception as ws_e:
                                self.logger.bind(tag=TAG).error(f"🎵 发送TTS错误消息失败: {ws_e}")
                            consecutive_timeouts = 0  # Reset after handling max timeouts
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"TTS出错: {e}")
                    consecutive_timeouts = 0  # 重置超时计数器
                    continue  # 发生错误时也跳过
                
                if not self.client_abort and 'audio_datas' in locals() and audio_datas is not None:
                    # 只有在有有效音频数据且客户端未中断时才发送
                    self.audio_play_queue.put((audio_datas, text, text_index))
                elif self.client_abort:
                    self.logger.bind(tag=TAG).debug(f"🔇 TTS任务跳过 - 客户端已中断: {text_index}: {text[:30] if text else 'None'}...")
                else:
                    self.logger.bind(tag=TAG).error("TTS失败: 未生成有效的音频数据")
                if (
                    self.tts.delete_audio_file
                    and tts_file is not None
                    and os.path.exists(tts_file)
                ):
                    os.remove(tts_file)
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"TTS任务处理错误: {e}")
                self.clearSpeakStatus()
                asyncio.run_coroutine_threadsafe(
                    self.websocket.send(
                        json.dumps(
                            {
                                "type": "tts",
                                "state": "stop",
                                "session_id": self.session_id,
                            }
                        )
                    ),
                    self.loop,
                )
                self.logger.bind(tag=TAG).error(
                    f"tts_priority priority_thread: {text} {e}"
                )

    def _audio_play_priority_thread(self):
        while not self.stop_event.is_set():
            text = None
            try:
                try:
                    item = self.audio_play_queue.get(timeout=1)
                    if item is None:
                        # 【CPU修复】收到毒丸信号，退出线程
                        self.logger.bind(tag=TAG).info(f"🔌 音频播放线程收到毒丸信号，正在退出")
                        break
                    audio_datas, text, text_index = item
                except queue.Empty:
                    if self.stop_event.is_set():
                        break
                    continue
                
                # 检查是否被用户中断
                if self.client_abort:
                    self.logger.bind(tag=TAG).debug(f"🔇 音频播放跳过 - 客户端已中断: {text_index}: {text[:30] if text else 'None'}...")
                    continue
                    
                future = asyncio.run_coroutine_threadsafe(
                    sendAudioMessage(self, audio_datas, text, text_index), self.loop
                )
                future.result()
            except Exception as e:
                self.logger.bind(tag=TAG).error(
                    f"audio_play_priority priority_thread: {text} {e}"
                )

    def _clear_tts_queue(self):
        """清空TTS队列中的待处理任务"""
        try:
            while True:
                try:
                    item = self.tts_queue.get_nowait()
                    if item is not None:
                        future, text_index = item
                        if future and not future.done():
                            future.cancel()
                        self.logger.bind(tag=TAG).debug(f"🗑️ 已清理TTS任务: text_index={text_index}")
                except queue.Empty:
                    break
            self.logger.bind(tag=TAG).info("🗑️ TTS队列已清空")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"清空TTS队列失败: {e}")

    def _report_worker(self):
        """聊天记录上报工作线程"""
        while not self.stop_event.is_set():
            try:
                # 从队列获取数据，设置超时以便定期检查停止事件
                item = self.report_queue.get(timeout=1)
                if item is None:  # 检测毒丸对象
                    break

                type, text, audio_data = item

                try:
                    # 执行上报（传入二进制数据）
                    report(self, type, text, audio_data)
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"聊天记录上报线程异常: {e}")
                finally:
                    # 标记任务完成
                    self.report_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"聊天记录上报工作线程异常: {e}")

        self.logger.bind(tag=TAG).info("聊天记录上报线程已退出")


    def speak_and_play(self, text, text_index=0, is_redirect_response=False):
        if text is None or len(text) <= 0:
            self.logger.bind(tag=TAG).info(f"无需tts转换，query为空，{text}")
            return None, text, text_index

        # 【重要修复】检查连接状态，防止为已断开的客户端创建TTS请求
        if self.client_abort or self.is_connection_closed:
            self.logger.bind(tag=TAG).debug(f"🔇 TTS任务跳过 - 客户端已断开连接: text_index={text_index}, text='{text[:30]}...'")
            return None, text, text_index

        # 如果已经被拦截，只跳过非引导性回复的内容
        if getattr(self, 'content_blocked', False) and not is_redirect_response:
            self.logger.bind(tag=TAG).debug(f"🚫 TTS任务跳过 - 内容已被拦截: text_index={text_index}, text='{text[:30]}...'")
            return None, text, text_index
        
        # 对于引导性回复，记录日志
        if is_redirect_response:
            self.logger.bind(tag=TAG).info(f"🔄 处理引导性回复TTS: text_index={text_index}, text='{text[:30]}...'")
        elif getattr(self, 'content_blocked', False):
            # 这种情况不应该发生，但作为安全检查
            self.logger.bind(tag=TAG).warning(f"⚠️ 意外情况：content_blocked=True但is_redirect_response=False: text_index={text_index}")
            return None, text, text_index

        # 开始TTS延迟计时
        begin_time = time.time() * 1000

        tts_file = self.tts.to_tts(text, self.client_id)

         # 结束TTS延迟计时
        end_time = time.time() * 1000
        self.logger.bind(tag=TAG).info(f"【{text}】TTS延迟: {end_time - begin_time:.0f}毫秒")

        if tts_file is None:
            self.logger.bind(tag=TAG).info(f"TTS处理跳过或失败: {text}")
            return None, text, text_index
        self.logger.bind(tag=TAG).debug(f"TTS 文件生成完毕: {tts_file}")
        if self.max_output_size > 0:
            add_device_output(self.headers.get("device-id"), len(text))
        return tts_file, text, text_index

    def clearSpeakStatus(self):
        self.logger.bind(tag=TAG).debug(f"清除服务端讲话状态")
        self.asr_server_receive = True
        self.tts_last_text_index = -1
        self.tts_first_text_index = -1

    def recode_first_last_text(self, text, text_index=0):
        if self.tts_first_text_index == -1:
            self.logger.bind(tag=TAG).info(f"大模型说出第一句话: {text}")
            self.tts_first_text_index = text_index

            # 如果LLM计时已开始，则记录延迟
            if hasattr(self, 'latency_tracker') and self.latency_tracker.start_times.get("LLM") is not None:
                llm_latency = self.latency_tracker.end("LLM")
                self.logger.bind(tag=TAG).info(f"LLM延迟(到第一句): {llm_latency:.0f}毫秒")
        self.tts_last_text_index = text_index

    async def close(self, ws=None):
        """资源清理方法"""
        # 立即设置连接关闭标志，以便所有生成过程能够快速检测到
        self.is_connection_closed = True
        self.client_abort = True  # 设置客户端中断标志，立即停止LLM处理
        # 标记会话已关闭，立即通知LLM生成过程
        mark_session_closed(self.session_id)
        
        # 记录连接关闭的详细信息
        connection_duration = time.time() - getattr(self, 'connection_start_time', time.time())
        
        # 分析关闭原因
        import traceback
        call_stack = traceback.extract_stack()
        
        # 检查关闭原因，优先使用client_disconnected标志
        close_reason = "UNKNOWN"
        close_initiator = "SERVER"
        websocket_already_closed = False
        
        # 首先检查是否是客户端断开连接
        if getattr(self, 'client_disconnected', False):
            websocket_already_closed = True
            close_reason = "CLIENT_DISCONNECT"
            close_initiator = "CLIENT"
        elif ws is not None:
            try:
                # 检查WebSocket状态
                if hasattr(ws, 'state'):
                    from websockets.protocol import State
                    if ws.state in (State.CLOSING, State.CLOSED):
                        websocket_already_closed = True
                        # 检查close_code来确定关闭原因
                        if hasattr(ws, 'close_code') and ws.close_code is not None:
                            if ws.close_code == 1000:
                                close_reason = "CLIENT_NORMAL_CLOSE"
                                close_initiator = "CLIENT"
                            elif ws.close_code == 1001:
                                close_reason = "CLIENT_GOING_AWAY"
                                close_initiator = "CLIENT"
                            elif ws.close_code == 1006:
                                close_reason = "CLIENT_ABNORMAL_CLOSE"
                                close_initiator = "CLIENT"
                            else:
                                close_reason = f"CLIENT_CLOSE_CODE_{ws.close_code}"
                                close_initiator = "CLIENT"
                        else:
                            close_reason = "CLIENT_DISCONNECT"
                            close_initiator = "CLIENT"
            except Exception as e:
                self.logger.bind(tag=TAG).debug(f"检查WebSocket状态时出错: {e}")
        
        # 检查最近几层调用栈
        stack_info = []
        for frame in call_stack[-5:]:  # 查看最近5层调用
            stack_info.append(f"{frame.filename}:{frame.lineno} in {frame.name}")
            
        stack_str = " -> ".join(stack_info)
        
        # 如果WebSocket未关闭，则根据调用栈判断服务端关闭原因
        if not websocket_already_closed:
            if any("_save_and_close" in info for info in stack_info):
                close_reason = "NORMAL_CLEANUP"
                close_initiator = "CONNECTION_HANDLER"
            elif any("handle_connection" in info for info in stack_info):
                close_reason = "CONNECTION_END"
                close_initiator = "CONNECTION_HANDLER"
            elif any("_handle_connection" in info for info in stack_info):
                close_reason = "SERVER_CLEANUP"
                close_initiator = "WEBSOCKET_SERVER"
            elif self.client_abort:
                close_reason = "CLIENT_ABORT"
                close_initiator = "CLIENT"
        
        # 根据实际发起方显示不同的日志消息
        if close_initiator == "CLIENT":
            connection_status = "客户端断开连接"
        else:
            connection_status = "服务端关闭连接"
            
        self.logger.bind(tag=TAG).warning(
            f"🔌 {connection_status} - "
            f"原因: {close_reason}, "
            f"发起方: {close_initiator}, "
            f"会话ID: {self.session_id}, "
            f"客户端: {self.client_id}, "
            f"设备ID: {self.device_id}, "
            f"连接时长: {connection_duration:.2f}秒"
        )
        
        # 在DEBUG级别记录详细调用栈
        self.logger.bind(tag=TAG).debug(f"🔌 关闭调用栈: {stack_str}")
        
        # 【优先处理】立即清理TTS连接，防止CPU占用问题
        try:
            self.logger.bind(tag=TAG).info(f"🔌 【优先清理】开始清理TTS连接")
            try:
                from core.providers.tts.doubao import TTSProvider
                await TTSProvider.close_client_connections(self.client_id)
                self.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS连接清理完成")
                
                # 【CPU修复】等待一小段时间确保TTS工作协程完全退出
                import asyncio
                await asyncio.sleep(0.5)  # 等待500ms让TTS协程完全停止
                self.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS协程停止等待完成")
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"🔌 【优先清理】清理TTS连接时出错: {e}")
                # 即使TTS清理失败，也要尝试强制停止
                try:
                    import asyncio
                    await asyncio.sleep(0.1)  # 短暂等待
                    self.logger.bind(tag=TAG).warning(f"🔌 【优先清理】TTS清理失败，已尝试强制等待")
                except:
                    pass
        except Exception as fatal_e:
            self.logger.bind(tag=TAG).error(f"🔌 【优先清理】TTS清理出现致命错误: {fatal_e}")
        finally:
            self.logger.bind(tag=TAG).info(f"🔌 【优先清理】TTS清理流程完成（无论成功或失败）")
        

        # 清理TTS WebSocket连接
        if self.client_id and hasattr(self, 'tts') and self.tts:
            try:
                # 检查是否是豆包TTS提供者
                if hasattr(self.tts, '__class__') and 'doubao' in self.tts.__class__.__module__:
                    from core.providers.tts.doubao import TTSProvider as DoubaoTTSProvider
                    await DoubaoTTSProvider.close_client_connections(self.client_id)
                    self.logger.bind(tag=TAG).info(f"已清理客户端 {self.client_id} 的豆包TTS WebSocket连接")
            except Exception as e:
                self.logger.bind(tag=TAG).warning(f"清理客户端 {self.client_id} 的TTS WebSocket连接时出错: {e}")

        # 保存用户设置到Redis（使用user_profile_manager统一管理）
        if hasattr(self, 'user_profile_manager') and self.user_profile_manager:
            try:
                self.user_profile_manager.save_user_settings()
                self.logger.bind(tag=TAG).info(f"已保存用户 {self.client_id} 的设置")
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"保存用户 {self.client_id} 的设置失败: {e}")

        # 移除超时任务清理逻辑

        # 清理MCP资源
        if hasattr(self, "mcp_manager") and self.mcp_manager:
            await self.mcp_manager.cleanup_all()

        # 触发停止事件并清理资源
        if self.stop_event:
            self.stop_event.set()

        # 立即关闭线程池
        self.logger.bind(tag=TAG).info(f"🔌 断开连接：开始清理ThreadPoolExecutor")
        if self.executor:
            try:
                self.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor存在，开始清理")
                # 【CPU修复】先尝试取消所有正在等待的futures，防止CPU占用
                # 获取所有提交到executor但尚未完成的futures
                submitted_futures = []
                # 从TTS队列中获取所有等待的futures
                temp_tts_items = []
                tts_queue_size = self.tts_queue.qsize()
                self.logger.bind(tag=TAG).info(f"🔌 TTS队列大小: {tts_queue_size}")
                
                while not self.tts_queue.empty():
                    try:
                        item = self.tts_queue.get_nowait()
                        temp_tts_items.append(item)
                        if hasattr(item, '__iter__') and len(item) >= 1:
                            future = item[0]
                            if hasattr(future, 'cancel'):
                                submitted_futures.append(future)
                    except:
                        break
                
                self.logger.bind(tag=TAG).info(f"🔌 从TTS队列中发现 {len(submitted_futures)} 个futures")
                
                # 取消所有等待中的futures
                cancelled_count = 0
                for future in submitted_futures:
                    try:
                        if future.cancel():
                            cancelled_count += 1
                    except:
                        pass
                
                if cancelled_count > 0:
                    self.logger.bind(tag=TAG).info(f"🔌 已取消 {cancelled_count} 个等待中的TTS任务")
                else:
                    self.logger.bind(tag=TAG).info(f"🔌 无等待中的TTS任务需要取消")
                
                # 使用兼容的方式关闭executor
                self.logger.bind(tag=TAG).info(f"🔌 开始关闭ThreadPoolExecutor")
                try:
                    # 尝试使用Python 3.9+的参数
                    self.executor.shutdown(wait=False, cancel_futures=True)
                    self.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor已关闭 (使用cancel_futures=True)")
                except TypeError:
                    # Python 3.8及以下版本不支持cancel_futures参数
                    self.executor.shutdown(wait=False)
                    self.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor已关闭 (不支持cancel_futures参数)")
                    
                # 【CPU修复增强】强制终止所有executor线程，防止CPU占用100%
                import threading
                active_threads = threading.active_count()
                self.logger.bind(tag=TAG).info(f"🔌 当前活跃线程数: {active_threads}")
                
                # 获取executor内部的线程并强制清理
                if hasattr(self.executor, '_threads'):
                    executor_threads = getattr(self.executor, '_threads', set())
                    self.logger.bind(tag=TAG).info(f"🔌 Executor线程数: {len(executor_threads)}")
                    
                    # 创建一个新的set来避免迭代时修改原集合
                    threads_to_clean = list(executor_threads)
                    for thread in threads_to_clean:
                        if thread.is_alive():
                            self.logger.bind(tag=TAG).warning(f"🔌 发现存活的executor线程: {thread.name}")
                            
                    # 【关键修复】清空executor的线程集合，防止悬挂引用
                    executor_threads.clear()
                    self.logger.bind(tag=TAG).info(f"🔌 已清空executor线程集合")
                            
                # 等待一小段时间让线程自然退出，然后强制清理
                time.sleep(0.2)
                
                # 【额外清理】强制设置executor为broken状态
                if hasattr(self.executor, '_broken'):
                    self.executor._broken = True
                    self.logger.bind(tag=TAG).info(f"🔌 已设置executor为broken状态")
                
                final_active_threads = threading.active_count()
                self.logger.bind(tag=TAG).info(f"🔌 最终活跃线程数: {final_active_threads}")
                
                # 如果仍有非daemon线程，记录警告
                remaining_non_daemon = []
                for t in threading.enumerate():
                    if t.is_alive() and not t.daemon and 'ThreadPoolExecutor' in t.name:
                        remaining_non_daemon.append(t.name)
                        
                if remaining_non_daemon:
                    self.logger.bind(tag=TAG).warning(f"🔌 仍有 {len(remaining_non_daemon)} 个non-daemon executor线程存活: {remaining_non_daemon}")
                    self.logger.bind(tag=TAG).warning(f"🔌 这些线程现在应该是daemon线程，进程退出时会自动终止")
            except Exception as e:
                self.logger.bind(tag=TAG).warning(f"🔌 关闭ThreadPoolExecutor时出错: {e}")
                import traceback
                self.logger.bind(tag=TAG).error(f"🔌 ThreadPoolExecutor关闭错误堆栈: {traceback.format_exc()}")
            finally:
                self.executor = None
                self.logger.bind(tag=TAG).info(f"🔌 ThreadPoolExecutor清理完成")
        else:
            self.logger.bind(tag=TAG).info(f"🔌 无ThreadPoolExecutor需要清理")

        # 添加毒丸对象到上报队列确保线程退出
        self.logger.bind(tag=TAG).info(f"🔌 断开连接：添加毒丸到上报队列")
        self.report_queue.put(None)

        # 清空任务队列并终止线程
        self.logger.bind(tag=TAG).info(f"🔌 断开连接：开始清空任务队列并终止线程")
        self.clear_queues(send_poison_pill=True)

        # 关闭WebSocket连接
        self.logger.bind(tag=TAG).info(f"🔌 断开连接：关闭WebSocket")
        if ws:
            await ws.close()
            self.logger.bind(tag=TAG).info(f"🔌 传入的WebSocket已关闭")
        elif self.websocket:
            await self.websocket.close()
            self.logger.bind(tag=TAG).info(f"🔌 实例WebSocket已关闭")
        
        # TTS清理已在方法开始时优先处理，避免重复清理
        self.logger.bind(tag=TAG).info(f"🔌 TTS清理已在优先阶段完成，跳过重复清理")
        
        self.logger.bind(tag=TAG).info("🔌 连接资源已释放")
        
        # 【CPU修复调试】列出所有仍然活跃的线程
        import threading
        active_threads = threading.enumerate()
        self.logger.bind(tag=TAG).info(f"🔌 当前所有活跃线程 ({len(active_threads)}):")
        for thread in active_threads:
            self.logger.bind(tag=TAG).info(f"  - {thread.name} (daemon={thread.daemon}, alive={thread.is_alive()})")
        
        # 清理会话状态，防止内存泄漏
        self.logger.bind(tag=TAG).info(f"🔌 断开连接：清理会话状态")
        cleanup_session(self.session_id)
        self.logger.bind(tag=TAG).info(f"🔌 会话状态清理完成")
        
        # 【重要修复】不要取消所有异步任务！这会导致服务器退出
        # 只记录当前任务状态，不进行任何取消操作
        try:
            import asyncio
            try:
                current_loop = asyncio.get_running_loop()
                if current_loop and not current_loop.is_closed():
                    # 只记录任务状态，不取消任何任务
                    pending_tasks = [task for task in asyncio.all_tasks(current_loop) if not task.done()]
                    self.logger.bind(tag=TAG).info(f"🔌 当前有 {len(pending_tasks)} 个运行中的异步任务")
                    
                    # 记录主要任务状态（用于调试），但不取消
                    main_tasks = []
                    connection_tasks = []
                    for task in pending_tasks:
                        task_name = str(task.get_coro())
                        if 'main()' in task_name or 'start()' in task_name or 'monitor_stdin()' in task_name:
                            main_tasks.append(task_name)
                        elif 'handle_connection' in task_name or 'conn_handler' in task_name:
                            connection_tasks.append(task_name)
                    
                    if main_tasks:
                        self.logger.bind(tag=TAG).debug(f"🔌 主要服务器任务仍在运行: {len(main_tasks)}个")
                    if connection_tasks:
                        self.logger.bind(tag=TAG).debug(f"🔌 连接处理任务: {len(connection_tasks)}个")
                        
                    self.logger.bind(tag=TAG).info(f"🔌 客户端连接清理完成，服务器继续运行")
                else:
                    self.logger.bind(tag=TAG).info(f"🔌 事件循环已关闭或不存在")
            except RuntimeError as e:
                if "no running event loop" in str(e):
                    self.logger.bind(tag=TAG).info(f"🔌 事件循环已停止")
                else:
                    self.logger.bind(tag=TAG).warning(f"🔌 获取事件循环时出错: {e}")
        except Exception as e:
            self.logger.bind(tag=TAG).warning(f"🔌 检查异步任务状态时出错: {e}")
        
        # 【CPU调试】最终检查事件循环任务状态
        try:
            try:
                current_loop = asyncio.get_running_loop()
                all_tasks = asyncio.all_tasks(current_loop)
                running_tasks = [task for task in all_tasks if not task.done()]
                self.logger.bind(tag=TAG).info(f"🔌 当前事件循环中的任务状态: 总任务数={len(all_tasks)}, 运行中任务数={len(running_tasks)}")
                
                # 记录前5个运行中任务的详细信息
                for i, task in enumerate(running_tasks[:5]):
                    try:
                        task_name = getattr(task, '_name', 'unknown')
                        task_coro = getattr(task, '_coro', None)
                        coro_name = getattr(task_coro, '__name__', 'unknown') if task_coro else 'unknown'
                        self.logger.bind(tag=TAG).info(f"🔌 运行中任务 #{i+1}: name={task_name}, coro={coro_name}, done={task.done()}, cancelled={task.cancelled()}")
                    except Exception as e:
                        self.logger.bind(tag=TAG).warning(f"🔌 获取任务 #{i+1} 信息失败: {e}")
            except RuntimeError as e:
                if "no running event loop" in str(e):
                    self.logger.bind(tag=TAG).info(f"🔌 事件循环已停止，无法检查异步任务状态")
                else:
                    self.logger.bind(tag=TAG).warning(f"🔌 检查事件循环时出错: {e}")
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"🔌 检查异步任务状态失败: {e}")
            
        self.logger.bind(tag=TAG).info(f"🔌🔌🔌 客户端 {self.client_id} 完全断开连接处理完成 🔌🔌🔌")

    def clear_queues(self, send_poison_pill=False):
        """清空任务队列

        Args:
            send_poison_pill (bool): 是否发送毒丸信号终止线程
                - False: 只清空队列，不终止线程（用于用户说话中断）
                - True: 清空队列并终止线程（用于连接关闭）
        """
        # 清空所有任务队列
        self.logger.bind(tag=TAG).debug(
            f"开始清理: TTS队列大小={self.tts_queue.qsize()}, 音频队列大小={self.audio_play_queue.qsize()}, 发送毒丸信号={send_poison_pill}"
        )
        for q in [self.tts_queue, self.audio_play_queue]:
            if not q:
                continue
            while not q.empty():
                try:
                    q.get_nowait()
                except queue.Empty:
                    continue
            q.queue.clear()

            # 只有在明确要求时才发送毒丸信号
            if send_poison_pill:
                try:
                    q.put(None, block=False)  # 发送毒丸信号
                    self.logger.bind(tag=TAG).debug(f"已向队列发送毒丸信号: {type(q).__name__}")
                except:
                    pass
        self.logger.bind(tag=TAG).debug(
            f"清理结束: TTS队列大小={self.tts_queue.qsize()}, 音频队列大小={self.audio_play_queue.qsize()}"
        )

    def reset_vad_states(self):
        self.client_audio_buffer = bytearray()
        self.client_have_voice = False
        self.client_have_voice_last_time = 0
        self.client_voice_stop = False
        self.logger.bind(tag=TAG).debug("VAD states reset.")



