#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import asyncio
import json
from typing import Dict, Any, Optional, Tuple
from config.logger import setup_logging

TAG = "PolicyCheck"

class PolicyCheck:
    """内容安全检测类，用于检测用户问题和LLM回答是否违规"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.ask_endpoint = "/api/risk-detect/v1/text/ask"
        self.answer_endpoint = "/api/risk-detect/v1/text/answer"
        self.logger = setup_logging()
        
        # 默认配置
        self.base_url = "https://ai-guardian-api.apusai.com"
        self.api_key = None
        self.scene = "dazi"
        self.timeout = 3
        self.enabled = True
        
        # 从配置中获取设置
        if config and 'policy_check' in config:
            policy_config = config['policy_check']
            self.enabled = policy_config.get('enabled', True)
            self.base_url = policy_config.get('base_url', self.base_url)
            self.api_key = policy_config.get('api_key', self.api_key)
            self.timeout = policy_config.get('timeout', self.timeout)
            self.scene = policy_config.get('scene', self.scene)
        
        if not self.api_key and self.enabled:
            self.logger.bind(tag=TAG).warning("Policy check enabled but no API key configured")
            self.enabled = False
            
    async def check_question(self, content: str, user_id: str = "123456") -> Dict[str, Any]:
        """
        检测用户提问内容是否违规
        
        Args:
            content: 用户提问内容
            user_id: 用户ID
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "rewrite_content": None,
                "error": None,
                "raw_response": None
            }
        url = self.base_url + self.ask_endpoint
        payload = {
            "content": content,
            "userId": user_id,
            "scene": self.scene
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 使用asyncio运行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout,
                    headers=headers
                )
            )
            
            result = response.json()
            return self._parse_result(result, "question")
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"问题检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"问题检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    async def check_answer(self, question: str, answer: str) -> Dict[str, Any]:
        """
        检测LLM回答内容是否违规
        
        Args:
            question: 用户问题
            answer: LLM回答
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "rewrite_content": None,
                "error": None,
                "raw_response": None
            }
        url = self.base_url + self.answer_endpoint
        payload = {
            "question": question,
            "answer": answer,
            "scene": self.scene
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 使用asyncio运行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url, 
                    json=payload, 
                    timeout=self.timeout,
                    headers=headers
                )
            )
            
            result = response.json()
            return self._parse_result(result, "answer")
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"回答检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"回答检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    def _parse_result(self, result: Dict[str, Any], check_type: str) -> Dict[str, Any]:
        """
        解析API返回结果
        
        Args:
            result: API返回的原始结果
            check_type: 检测类型（question/answer）
            
        Returns:
            标准化的检测结果
        """
        parsed_result = {
            "is_blocked": False,
            "detect_result": None,
            "label_code": None,
            "rewrite_content": None,
            "error": None,
            "raw_response": result
        }
        
        if 'error' in result:
            parsed_result["error"] = result['error']
            return parsed_result
            
        if 'data' in result:
            data = result['data']
            detect_result = data.get('detectResult', 'PASS')
            parsed_result["detect_result"] = detect_result
            
            # 如果检测结果不是PASS，则认为被拦截
            if detect_result != 'PASS':
                parsed_result["is_blocked"] = True
                parsed_result["label_code"] = data.get('labelCode')
                parsed_result["rewrite_content"] = data.get('rewriteContent')
                
                self.logger.bind(tag=TAG).warning(
                    f"{check_type}内容被拦截 - "
                    f"检测结果: {detect_result}, "
                    f"违规标签: {parsed_result['label_code']}"
                )
            else:
                self.logger.bind(tag=TAG).debug(f"{check_type}内容检测通过")
        
        return parsed_result
    
    async def generate_redirect_response(self, llm_provider, blocked_content: str = None) -> str:
        """
        生成引导性回复，当内容被拦截时使用
        
        Args:
            llm_provider: LLM提供者实例
            blocked_content: 被拦截的内容（可选）
            
        Returns:
            引导性回复内容
        """
        try:
            # 构建引导性提示词
            redirect_prompt = (
                "你是一个贴心的闺蜜助手。用户刚才的话题可能不太适合聊，"
                "请你用温柔、友善的语气告诉用户咱们换个话题聊吧，"
                "比如一种说法是：'哎呀，咱们闺蜜之间聊天，不聊这个话题好不好？'你想想其它类似说法。"
                "然后自然地引导到一个轻松愉快的新话题上，"
                "比如聊聊美食、电影、音乐、生活趣事等。"
                "请直接回复内容，不要加任何前缀。"
            )
            
            # 调用LLM生成引导性回复
            redirect_response = llm_provider.response_no_stream(redirect_prompt, "")
            
            # 提取文本内容
            if isinstance(redirect_response, dict) and 'content' in redirect_response:
                return redirect_response['content']
            elif isinstance(redirect_response, str):
                return redirect_response
            else:
                # 如果LLM调用失败，返回默认引导语
                return "哎呀，咱们闺蜜之间聊天，不聊这个话题好不好？你最近看了什么好电影吗？"
                
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成引导性回复失败: {str(e)}")
            # 返回默认引导语
            return "哎呀，咱们闺蜜之间聊天，不聊这个话题好不好？你最近有什么买什么化妆品吗？"